import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "../ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { Progress } from "../ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "../ui/tabs";
import { 
  Play, 
  Pause, 
  Square, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  Users,
  FileText,
  Palette,
  Code,
  FolderOpen,
  Shield
} from "lucide-react";
import { toast } from "sonner";

interface AgentWorkflowProps {
  onWorkflowComplete?: (files: any[]) => void;
  onWorkflowError?: (error: string) => void;
}

interface WorkflowRequirements {
  description: string;
  type: "landing_page" | "portfolio" | "blog" | "ecommerce" | "corporate" | "custom";
  style: "modern" | "classic" | "minimal" | "creative" | "professional";
  colorScheme?: string;
  features: string[];
  targetAudience?: string;
  content?: {
    title?: string;
    sections?: string[];
    copyTone?: "formal" | "casual" | "friendly" | "professional" | "creative";
  };
}

interface WorkflowStatus {
  sessionId: string;
  status: "planning" | "executing" | "reviewing" | "completed" | "failed" | "paused";
  activeAgent: string | null;
  progress: {
    completed: number;
    total: number;
    currentStep: string;
  };
  requiresApproval: boolean;
  files: any[];
  errors: string[];
}

const agentIcons = {
  supervisor: Users,
  content_generation: FileText,
  design: Palette,
  code_generation: Code,
  file_management: FolderOpen,
  quality_assurance: Shield,
};

const agentNames = {
  supervisor: "Supervisor",
  content_generation: "Content Generation",
  design: "Design",
  code_generation: "Code Generation", 
  file_management: "File Management",
  quality_assurance: "Quality Assurance",
};

export default function AgentWorkflow({ onWorkflowComplete, onWorkflowError }: AgentWorkflowProps) {
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [workflowStatus, setWorkflowStatus] = useState<WorkflowStatus | null>(null);
  const [requirements, setRequirements] = useState<WorkflowRequirements>({
    description: "",
    type: "landing_page",
    style: "modern",
    features: [],
    targetAudience: "",
  });
  const [showRequirements, setShowRequirements] = useState(true);

  const startWorkflow = async () => {
    if (!requirements.description.trim()) {
      toast.error("Please provide a website description");
      return;
    }

    try {
      setIsRunning(true);
      setShowRequirements(false);
      
      const sessionId = `workflow_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      
      const response = await fetch("/api/agents/start-workflow", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          requirements,
          sessionId,
        }),
      });

      const result = await response.json();
      
      if (result.ok) {
        setWorkflowStatus({
          sessionId,
          status: "planning",
          activeAgent: null,
          progress: { completed: 0, total: 5, currentStep: "Initializing" },
          requiresApproval: false,
          files: [],
          errors: [],
        });
        
        // Start streaming updates
        startStatusPolling(sessionId);
        toast.success("Agent workflow started successfully!");
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error("Failed to start workflow:", error);
      toast.error("Failed to start agent workflow");
      setIsRunning(false);
      if (onWorkflowError) {
        onWorkflowError(error instanceof Error ? error.message : "Unknown error");
      }
    }
  };

  const pauseWorkflow = async () => {
    if (!workflowStatus) return;
    
    try {
      setIsPaused(true);
      // API call to pause workflow would go here
      toast.info("Workflow paused");
    } catch (error) {
      console.error("Failed to pause workflow:", error);
      toast.error("Failed to pause workflow");
    }
  };

  const resumeWorkflow = async () => {
    if (!workflowStatus) return;
    
    try {
      setIsPaused(false);
      // API call to resume workflow would go here
      toast.info("Workflow resumed");
    } catch (error) {
      console.error("Failed to resume workflow:", error);
      toast.error("Failed to resume workflow");
    }
  };

  const stopWorkflow = async () => {
    try {
      setIsRunning(false);
      setIsPaused(false);
      setWorkflowStatus(null);
      setShowRequirements(true);
      toast.info("Workflow stopped");
    } catch (error) {
      console.error("Failed to stop workflow:", error);
      toast.error("Failed to stop workflow");
    }
  };

  const approveStep = async (approved: boolean) => {
    if (!workflowStatus) return;
    
    try {
      const response = await fetch(`/api/agents/approve/${workflowStatus.sessionId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ approved }),
      });

      const result = await response.json();
      
      if (result.ok) {
        setWorkflowStatus(prev => prev ? {
          ...prev,
          requiresApproval: false,
        } : null);
        
        toast.success(approved ? "Step approved" : "Step rejected");
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error("Failed to approve step:", error);
      toast.error("Failed to process approval");
    }
  };

  const startStatusPolling = (sessionId: string) => {
    const pollStatus = async () => {
      try {
        const response = await fetch(`/api/agents/status/${sessionId}`);
        const result = await response.json();
        
        if (result.ok && result.state) {
          setWorkflowStatus(result.state);
          
          if (result.state.status === "completed") {
            setIsRunning(false);
            toast.success("Workflow completed successfully!");
            if (onWorkflowComplete) {
              onWorkflowComplete(result.state.files || []);
            }
            return;
          }
          
          if (result.state.status === "failed") {
            setIsRunning(false);
            toast.error("Workflow failed");
            if (onWorkflowError) {
              onWorkflowError(result.state.errors?.join(", ") || "Workflow failed");
            }
            return;
          }
        }
      } catch (error) {
        console.error("Status polling error:", error);
      }
      
      // Continue polling if workflow is still running
      if (isRunning && !isPaused) {
        setTimeout(pollStatus, 2000);
      }
    };
    
    pollStatus();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "bg-green-500";
      case "failed": return "bg-red-500";
      case "executing": return "bg-blue-500";
      case "paused": return "bg-yellow-500";
      default: return "bg-gray-500";
    }
  };

  const getAgentStatus = (agentType: string) => {
    if (!workflowStatus) return "pending";
    if (workflowStatus.activeAgent === agentType) return "active";
    if (workflowStatus.progress.completed > getAgentOrder(agentType)) return "completed";
    return "pending";
  };

  const getAgentOrder = (agentType: string) => {
    const order = {
      content_generation: 0,
      design: 1,
      code_generation: 2,
      file_management: 3,
      quality_assurance: 4,
    };
    return order[agentType as keyof typeof order] || 0;
  };

  if (showRequirements) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            AI Agent Workflow
          </CardTitle>
          <CardDescription>
            Configure your website requirements and let our AI agents build it for you
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Website Description</label>
            <textarea
              className="w-full p-3 border rounded-lg resize-none"
              rows={3}
              placeholder="Describe the website you want to create..."
              value={requirements.description}
              onChange={(e) => setRequirements(prev => ({ ...prev, description: e.target.value }))}
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Website Type</label>
              <select
                className="w-full p-2 border rounded-lg"
                value={requirements.type}
                onChange={(e) => setRequirements(prev => ({ ...prev, type: e.target.value as any }))}
              >
                <option value="landing_page">Landing Page</option>
                <option value="portfolio">Portfolio</option>
                <option value="blog">Blog</option>
                <option value="ecommerce">E-commerce</option>
                <option value="corporate">Corporate</option>
                <option value="custom">Custom</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Style</label>
              <select
                className="w-full p-2 border rounded-lg"
                value={requirements.style}
                onChange={(e) => setRequirements(prev => ({ ...prev, style: e.target.value as any }))}
              >
                <option value="modern">Modern</option>
                <option value="classic">Classic</option>
                <option value="minimal">Minimal</option>
                <option value="creative">Creative</option>
                <option value="professional">Professional</option>
              </select>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Target Audience (Optional)</label>
            <input
              type="text"
              className="w-full p-2 border rounded-lg"
              placeholder="e.g., Small business owners, Tech professionals..."
              value={requirements.targetAudience}
              onChange={(e) => setRequirements(prev => ({ ...prev, targetAudience: e.target.value }))}
            />
          </div>
          
          <Button 
            onClick={startWorkflow}
            className="w-full"
            size="lg"
          >
            <Play className="h-4 w-4 mr-2" />
            Start AI Agent Workflow
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* Workflow Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                AI Agent Workflow
              </CardTitle>
              <CardDescription>
                {workflowStatus?.status === "completed" 
                  ? "Workflow completed successfully!"
                  : `Building your ${requirements.type} website...`
                }
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-2">
              {workflowStatus && (
                <Badge className={getStatusColor(workflowStatus.status)}>
                  {workflowStatus.status}
                </Badge>
              )}
              
              {isRunning && !isPaused && (
                <Button variant="outline" size="sm" onClick={pauseWorkflow}>
                  <Pause className="h-4 w-4" />
                </Button>
              )}
              
              {isPaused && (
                <Button variant="outline" size="sm" onClick={resumeWorkflow}>
                  <Play className="h-4 w-4" />
                </Button>
              )}
              
              <Button variant="outline" size="sm" onClick={stopWorkflow}>
                <Square className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {workflowStatus && (
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>{workflowStatus.progress.currentStep}</span>
                  <span>{workflowStatus.progress.completed}/{workflowStatus.progress.total}</span>
                </div>
                <Progress 
                  value={(workflowStatus.progress.completed / workflowStatus.progress.total) * 100} 
                  className="w-full"
                />
              </div>
              
              {workflowStatus.requiresApproval && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                    <span className="font-medium text-yellow-800">Approval Required</span>
                  </div>
                  <p className="text-sm text-yellow-700 mb-3">
                    The current step requires your approval to continue.
                  </p>
                  <div className="flex gap-2">
                    <Button size="sm" onClick={() => approveStep(true)}>
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Approve
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => approveStep(false)}>
                      Reject
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Agent Status */}
      <Tabs defaultValue="agents" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="agents">Agents</TabsTrigger>
          <TabsTrigger value="files">Files</TabsTrigger>
          <TabsTrigger value="logs">Logs</TabsTrigger>
        </TabsList>
        
        <TabsContent value="agents" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(agentNames).map(([type, name]) => {
              if (type === "supervisor") return null;
              
              const Icon = agentIcons[type as keyof typeof agentIcons];
              const status = getAgentStatus(type);
              const isActive = workflowStatus?.activeAgent === type;
              
              return (
                <Card key={type} className={`${isActive ? "ring-2 ring-blue-500" : ""}`}>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${
                        status === "completed" ? "bg-green-100 text-green-600" :
                        status === "active" ? "bg-blue-100 text-blue-600" :
                        "bg-gray-100 text-gray-600"
                      }`}>
                        <Icon className="h-4 w-4" />
                      </div>
                      
                      <div className="flex-1">
                        <h3 className="font-medium text-sm">{name}</h3>
                        <div className="flex items-center gap-1 mt-1">
                          {status === "completed" && <CheckCircle className="h-3 w-3 text-green-500" />}
                          {status === "active" && <Clock className="h-3 w-3 text-blue-500 animate-spin" />}
                          <span className="text-xs text-gray-500 capitalize">{status}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>
        
        <TabsContent value="files">
          <Card>
            <CardContent className="p-4">
              {workflowStatus?.files.length ? (
                <div className="space-y-2">
                  {workflowStatus.files.map((file, index) => (
                    <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                      <FileText className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{file.path}</span>
                      <Badge variant="outline" className="text-xs">{file.type}</Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">No files generated yet</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="logs">
          <Card>
            <CardContent className="p-4">
              {workflowStatus?.errors.length ? (
                <div className="space-y-2">
                  {workflowStatus.errors.map((error, index) => (
                    <div key={index} className="text-sm text-red-600 bg-red-50 p-2 rounded">
                      {error}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">No errors reported</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
