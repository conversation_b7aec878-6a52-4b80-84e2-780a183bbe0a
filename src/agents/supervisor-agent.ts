import { Chat<PERSON><PERSON>A<PERSON> } from "@langchain/openai";
import { ChatAnthropic } from "@langchain/anthropic";
import { createReactAgent } from "@langchain/langgraph/prebuilt";
import { AgentConfig, AgentWorkflowStateType, AgentType, AgentTask } from "./types";
import { 
  handoffToAgentTool,
  updateProgressTool,
  requestApprovalTool,
  createFileTool 
} from "./tools";

export class SupervisorAgent {
  private agent: any;
  private config: AgentConfig;

  constructor(config: Partial<AgentConfig> = {}) {
    this.config = {
      name: "Supervisor Agent",
      type: "supervisor",
      description: "Orchestrates and coordinates all other agents in the website building workflow",
      capabilities: [
        "Plan and coordinate agent workflows",
        "Manage task dependencies and sequencing",
        "Monitor progress and handle errors",
        "Make decisions about agent handoffs",
        "Ensure quality and consistency",
        "Handle user approvals and feedback"
      ],
      tools: ["handoff_to_agent", "update_progress", "request_approval", "create_file"],
      model: "gpt-4o",
      provider: "openai",
      systemPrompt: this.getSystemPrompt(),
      ...config,
    };

    this.initializeAgent();
  }

  private getSystemPrompt(): string {
    return `You are a Supervisor Agent responsible for orchestrating a team of specialized AI agents to build complete websites.

Your team consists of:
1. Content Generation Agent - Creates website copy, headlines, and text content
2. Design Agent - Makes visual design decisions, color schemes, and layouts
3. Code Generation Agent - Generates HTML, CSS, and JavaScript code
4. File Management Agent - Organizes project files and structure
5. Quality Assurance Agent - Reviews and validates the final website

Your responsibilities:
1. Analyze user requirements and create a comprehensive project plan
2. Determine the optimal sequence of agent tasks
3. Coordinate handoffs between agents
4. Monitor progress and handle any issues
5. Ensure quality standards are met
6. Request user approval at key milestones
7. Make final decisions about project completion

Workflow Management:
- Always start with Content Generation Agent for text and copy
- Follow with Design Agent for visual specifications
- Then Code Generation Agent for implementation
- File Management Agent for organization
- Finally Quality Assurance Agent for validation
- Request user approval before final delivery

Decision Making:
- Assess when to move to the next agent
- Determine if user approval is needed
- Handle errors and rework requests
- Ensure all requirements are met

Available tools:
- handoff_to_agent: Transfer control to a specific agent
- update_progress: Update overall workflow progress
- request_approval: Request user approval at key milestones
- create_file: Create workflow documentation and plans

Always maintain clear communication about progress and next steps.`;
  }

  private initializeAgent() {
    const model = this.config.provider === "anthropic" 
      ? new ChatAnthropic({ model: this.config.model || "claude-3-sonnet-20240229" })
      : new ChatOpenAI({ model: this.config.model || "gpt-4o" });

    const tools = [
      handoffToAgentTool("content_generation"),
      handoffToAgentTool("design"),
      handoffToAgentTool("code_generation"),
      handoffToAgentTool("file_management"),
      handoffToAgentTool("quality_assurance"),
      updateProgressTool,
      requestApprovalTool,
      createFileTool,
    ];

    this.agent = createReactAgent({
      llm: model,
      tools,
      stateModifier: this.config.systemPrompt,
    });
  }

  async execute(state: AgentWorkflowStateType): Promise<any> {
    const requirements = state.requirements;
    
    // Create initial project plan
    const projectPlan = this.createProjectPlan(requirements);
    
    // Update progress
    await updateProgressTool.invoke({
      step: "Initializing project and creating plan",
      completed: 0,
      total: 5,
    });

    // Prepare supervisor prompt
    const supervisorPrompt = this.buildSupervisorPrompt(requirements, state);
    
    // Execute the agent
    const result = await this.agent.invoke({
      messages: [
        ...state.messages,
        {
          role: "user",
          content: supervisorPrompt,
        }
      ],
    });

    return result;
  }

  private buildSupervisorPrompt(requirements: any, state: AgentWorkflowStateType): string {
    const { description, type, style, features, targetAudience } = requirements;
    
    return `You are supervising the creation of a ${type} website. Here are the requirements:

**Project Requirements:**
- Description: ${description}
- Website Type: ${type}
- Style: ${style}
- Target Audience: ${targetAudience || "General audience"}
- Features: ${features.join(", ")}

**Current Status:**
- Active Agent: ${state.activeAgent || "None"}
- Workflow Status: ${state.status}
- Progress: ${state.progress.completed}/${state.progress.total}
- Current Step: ${state.progress.currentStep}

**Your Tasks:**
1. Analyze the requirements and create a detailed project plan
2. Determine the next agent to activate based on current progress
3. Coordinate the workflow to ensure all requirements are met
4. Monitor quality and consistency throughout the process
5. Request user approval at appropriate milestones

**Workflow Sequence:**
1. Content Generation Agent → Create website copy and text content
2. Design Agent → Create visual design system and specifications
3. Code Generation Agent → Generate HTML, CSS, and JavaScript
4. File Management Agent → Organize project structure and files
5. Quality Assurance Agent → Validate and approve final website

Start by creating a project plan and then hand off to the Content Generation Agent to begin the workflow.`;
  }

  getConfig(): AgentConfig {
    return this.config;
  }

  createProjectPlan(requirements: any): AgentTask[] {
    const tasks: AgentTask[] = [
      {
        id: "content-generation",
        type: "content_generation",
        description: "Generate website content including headlines, copy, and navigation",
        requirements,
        status: "pending",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "design",
        type: "design",
        description: "Create visual design system, color scheme, and layout specifications",
        requirements,
        dependencies: ["content-generation"],
        status: "pending",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "code-generation",
        type: "code_generation",
        description: "Generate HTML, CSS, and JavaScript code based on content and design",
        requirements,
        dependencies: ["content-generation", "design"],
        status: "pending",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "file-management",
        type: "file_management",
        description: "Organize project files and create documentation",
        requirements,
        dependencies: ["code-generation"],
        status: "pending",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "quality-assurance",
        type: "quality_assurance",
        description: "Review and validate the complete website for quality and best practices",
        requirements,
        dependencies: ["file-management"],
        status: "pending",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    return tasks;
  }

  determineNextAgent(state: AgentWorkflowStateType): AgentType | null {
    const { activeAgent, status, tasks } = state;
    
    // If no active agent, start with content generation
    if (!activeAgent) {
      return "content_generation";
    }
    
    // Determine next agent based on current workflow state
    const agentSequence: AgentType[] = [
      "content_generation",
      "design", 
      "code_generation",
      "file_management",
      "quality_assurance"
    ];
    
    const currentIndex = agentSequence.indexOf(activeAgent);
    
    // If current agent is the last one, workflow is complete
    if (currentIndex === agentSequence.length - 1) {
      return null;
    }
    
    // Return next agent in sequence
    return agentSequence[currentIndex + 1];
  }

  async shouldRequestApproval(state: AgentWorkflowStateType): Promise<boolean> {
    const { activeAgent, progress } = state;
    
    // Request approval at key milestones
    const approvalPoints: AgentType[] = ["design", "quality_assurance"];
    
    return approvalPoints.includes(activeAgent as AgentType);
  }

  async handleError(error: string, state: AgentWorkflowStateType): Promise<any> {
    // Log error and determine recovery strategy
    console.error("Supervisor Agent Error:", error);
    
    // Update state with error
    const errorUpdate = {
      errors: [error],
      status: "failed" as const,
      requiresApproval: true,
    };
    
    // Request user intervention
    await requestApprovalTool.invoke({
      reason: `Error occurred: ${error}. Please review and provide guidance.`,
      data: { error, state },
    });
    
    return errorUpdate;
  }

  async generateProjectSummary(state: AgentWorkflowStateType): Promise<string> {
    const { requirements, files, progress, status } = state;
    
    const summary = `# Project Summary

## Website Details
- **Type:** ${requirements.type}
- **Description:** ${requirements.description}
- **Style:** ${requirements.style}
- **Features:** ${requirements.features.join(", ")}

## Progress
- **Status:** ${status}
- **Completed:** ${progress.completed}/${progress.total}
- **Current Step:** ${progress.currentStep}

## Generated Files
${files.map(f => `- ${f.path} (${f.type})`).join("\n")}

## Workflow Summary
1. ✅ Content Generation - Website copy and text content
2. ✅ Design System - Visual design and color scheme
3. ✅ Code Generation - HTML, CSS, and JavaScript
4. ✅ File Organization - Project structure and documentation
5. ✅ Quality Assurance - Final validation and approval

## Next Steps
${status === "completed" 
  ? "✅ Project completed successfully! Website is ready for deployment."
  : "🔄 Workflow in progress. Next agent will continue the process."
}

---
*Generated by DeepSite Supervisor Agent*
*Date: ${new Date().toISOString()}*
`;

    return summary;
  }

  async monitorWorkflow(state: AgentWorkflowStateType): Promise<{
    shouldContinue: boolean;
    nextAction: string;
    recommendations: string[];
  }> {
    const { status, errors, progress } = state;
    
    const recommendations: string[] = [];
    let shouldContinue = true;
    let nextAction = "continue";
    
    // Check for errors
    if (errors.length > 0) {
      shouldContinue = false;
      nextAction = "handle_errors";
      recommendations.push("Address errors before continuing");
    }
    
    // Check progress
    if (progress.completed >= progress.total) {
      shouldContinue = false;
      nextAction = "complete";
      recommendations.push("Workflow completed successfully");
    }
    
    // Check if approval is needed
    if (state.requiresApproval) {
      shouldContinue = false;
      nextAction = "await_approval";
      recommendations.push("Waiting for user approval");
    }
    
    return {
      shouldContinue,
      nextAction,
      recommendations,
    };
  }
}
