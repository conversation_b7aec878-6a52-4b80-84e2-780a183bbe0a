# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@ampproject/remapping@npm:^2.2.0, @ampproject/remapping@npm:^2.3.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/81d63cca5443e0f0c72ae18b544cc28c7c0ec2cea46e7cb888bb0e0f411a1191d0d6b7af798d54e30777d8d1488b2ec0732aac2be342d3d7d3ffd271c6f489ed
  languageName: node
  linkType: hard

"@anthropic-ai/sdk@npm:^0.39.0":
  version: 0.39.0
  resolution: "@anthropic-ai/sdk@npm:0.39.0"
  dependencies:
    "@types/node": "npm:^18.11.18"
    "@types/node-fetch": "npm:^2.6.4"
    abort-controller: "npm:^3.0.0"
    agentkeepalive: "npm:^4.2.1"
    form-data-encoder: "npm:1.7.2"
    formdata-node: "npm:^4.3.2"
    node-fetch: "npm:^2.6.7"
  checksum: 10c0/f3832ccf8af33fedd4dcb459804ccbebc84fa20b93ef39df1855060d6567444aa7ae83ea8dae09ca1f719f60ddf800b168f36f10dccaa3cab5e231e8adeb3a71
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/code-frame@npm:7.27.1"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.1.1"
  checksum: 10c0/5dd9a18baa5fce4741ba729acc3a3272c49c25cb8736c4b18e113099520e7ef7b545a4096a26d600e4416157e63e87d66db46aa3fbf0a5f2286da2705c12da00
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.27.2":
  version: 7.27.5
  resolution: "@babel/compat-data@npm:7.27.5"
  checksum: 10c0/da2751fcd0b58eea958f2b2f7ff7d6de1280712b709fa1ad054b73dc7d31f589e353bb50479b9dc96007935f3ed3cada68ac5b45ce93086b7122ddc32e60dc00
  languageName: node
  linkType: hard

"@babel/core@npm:^7.26.10":
  version: 7.27.4
  resolution: "@babel/core@npm:7.27.4"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.3"
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-module-transforms": "npm:^7.27.3"
    "@babel/helpers": "npm:^7.27.4"
    "@babel/parser": "npm:^7.27.4"
    "@babel/template": "npm:^7.27.2"
    "@babel/traverse": "npm:^7.27.4"
    "@babel/types": "npm:^7.27.3"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 10c0/d2d17b106a8d91d3eda754bb3f26b53a12eb7646df73c2b2d2e9b08d90529186bc69e3823f70a96ec6e5719dc2372fb54e14ad499da47ceeb172d2f7008787b5
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.27.3":
  version: 7.27.5
  resolution: "@babel/generator@npm:7.27.5"
  dependencies:
    "@babel/parser": "npm:^7.27.5"
    "@babel/types": "npm:^7.27.3"
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jsesc: "npm:^3.0.2"
  checksum: 10c0/8f649ef4cd81765c832bb11de4d6064b035ffebdecde668ba7abee68a7b0bce5c9feabb5dc5bb8aeba5bd9e5c2afa3899d852d2bd9ca77a711ba8c8379f416f0
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/helper-compilation-targets@npm:7.27.2"
  dependencies:
    "@babel/compat-data": "npm:^7.27.2"
    "@babel/helper-validator-option": "npm:^7.27.1"
    browserslist: "npm:^4.24.0"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: 10c0/f338fa00dcfea931804a7c55d1a1c81b6f0a09787e528ec580d5c21b3ecb3913f6cb0f361368973ce953b824d910d3ac3e8a8ee15192710d3563826447193ad1
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-module-imports@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/e00aace096e4e29290ff8648455c2bc4ed982f0d61dbf2db1b5e750b9b98f318bf5788d75a4f974c151bd318fd549e81dbcab595f46b14b81c12eda3023f51e8
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/helper-module-transforms@npm:7.27.3"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.3"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/fccb4f512a13b4c069af51e1b56b20f54024bcf1591e31e978a30f3502567f34f90a80da6a19a6148c249216292a8074a0121f9e52602510ef0f32dbce95ca01
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-plugin-utils@npm:7.27.1"
  checksum: 10c0/94cf22c81a0c11a09b197b41ab488d416ff62254ce13c57e62912c85700dc2e99e555225787a4099ff6bae7a1812d622c80fbaeda824b79baa10a6c5ac4cf69b
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-string-parser@npm:7.27.1"
  checksum: 10c0/8bda3448e07b5583727c103560bcf9c4c24b3c1051a4c516d4050ef69df37bb9a4734a585fe12725b8c2763de0a265aa1e909b485a4e3270b7cfd3e4dbe4b602
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-identifier@npm:7.27.1"
  checksum: 10c0/c558f11c4871d526498e49d07a84752d1800bf72ac0d3dad100309a2eaba24efbf56ea59af5137ff15e3a00280ebe588560534b0e894a4750f8b1411d8f78b84
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-option@npm:7.27.1"
  checksum: 10c0/6fec5f006eba40001a20f26b1ef5dbbda377b7b68c8ad518c05baa9af3f396e780bdfded24c4eef95d14bb7b8fd56192a6ed38d5d439b97d10efc5f1a191d148
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.27.4":
  version: 7.27.6
  resolution: "@babel/helpers@npm:7.27.6"
  dependencies:
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.6"
  checksum: 10c0/448bac96ef8b0f21f2294a826df9de6bf4026fd023f8a6bb6c782fe3e61946801ca24381490b8e58d861fee75cd695a1882921afbf1f53b0275ee68c938bd6d3
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.1.0, @babel/parser@npm:^7.20.7, @babel/parser@npm:^7.27.2, @babel/parser@npm:^7.27.4, @babel/parser@npm:^7.27.5":
  version: 7.27.5
  resolution: "@babel/parser@npm:7.27.5"
  dependencies:
    "@babel/types": "npm:^7.27.3"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10c0/f7faaebf21cc1f25d9ca8ac02c447ed38ef3460ea95be7ea760916dcf529476340d72a5a6010c6641d9ed9d12ad827c8424840277ec2295c5b082ba0f291220a
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-self@npm:^7.25.9":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-self@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/00a4f917b70a608f9aca2fb39aabe04a60aa33165a7e0105fd44b3a8531630eb85bf5572e9f242f51e6ad2fa38c2e7e780902176c863556c58b5ba6f6e164031
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-source@npm:^7.25.9":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-source@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5e67b56c39c4d03e59e03ba80692b24c5a921472079b63af711b1d250fc37c1733a17069b63537f750f3e937ec44a42b1ee6a46cd23b1a0df5163b17f741f7f2
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.1.2":
  version: 7.27.6
  resolution: "@babel/runtime@npm:7.27.6"
  checksum: 10c0/89726be83f356f511dcdb74d3ea4d873a5f0cf0017d4530cb53aa27380c01ca102d573eff8b8b77815e624b1f8c24e7f0311834ad4fb632c90a770fda00bd4c8
  languageName: node
  linkType: hard

"@babel/template@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/template@npm:7.27.2"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/ed9e9022651e463cc5f2cc21942f0e74544f1754d231add6348ff1b472985a3b3502041c0be62dc99ed2d12cfae0c51394bf827452b98a2f8769c03b87aadc81
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.27.1, @babel/traverse@npm:^7.27.3, @babel/traverse@npm:^7.27.4":
  version: 7.27.4
  resolution: "@babel/traverse@npm:7.27.4"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.3"
    "@babel/parser": "npm:^7.27.4"
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.3"
    debug: "npm:^4.3.1"
    globals: "npm:^11.1.0"
  checksum: 10c0/6de8aa2a0637a6ee6d205bf48b9e923928a02415771fdec60085ed754dcdf605e450bb3315c2552fa51c31a4662275b45d5ae4ad527ce55a7db9acebdbbbb8ed
  languageName: node
  linkType: hard

"@babel/types@npm:^7.0.0, @babel/types@npm:^7.20.7, @babel/types@npm:^7.27.1, @babel/types@npm:^7.27.3, @babel/types@npm:^7.27.6":
  version: 7.27.6
  resolution: "@babel/types@npm:7.27.6"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
  checksum: 10c0/39d556be114f2a6d874ea25ad39826a9e3a0e98de0233ae6d932f6d09a4b222923a90a7274c635ed61f1ba49bbd345329226678800900ad1c8d11afabd573aaf
  languageName: node
  linkType: hard

"@cfworker/json-schema@npm:^4.0.2":
  version: 4.1.1
  resolution: "@cfworker/json-schema@npm:4.1.1"
  checksum: 10c0/b5253486d346b7de6feec9c73954f612b11019dacb9023d710a5666df2f5fc145dd88b6b913c88726c6d97e2e258a515fa2cab177f58b18da6bac3738cbc4739
  languageName: node
  linkType: hard

"@emnapi/core@npm:^1.4.3":
  version: 1.4.3
  resolution: "@emnapi/core@npm:1.4.3"
  dependencies:
    "@emnapi/wasi-threads": "npm:1.0.2"
    tslib: "npm:^2.4.0"
  checksum: 10c0/e30101d16d37ef3283538a35cad60e22095aff2403fb9226a35330b932eb6740b81364d525537a94eb4fb51355e48ae9b10d779c0dd1cdcd55d71461fe4b45c7
  languageName: node
  linkType: hard

"@emnapi/runtime@npm:^1.4.3":
  version: 1.4.3
  resolution: "@emnapi/runtime@npm:1.4.3"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10c0/3b7ab72d21cb4e034f07df80165265f85f445ef3f581d1bc87b67e5239428baa00200b68a7d5e37a0425c3a78320b541b07f76c5530f6f6f95336a6294ebf30b
  languageName: node
  linkType: hard

"@emnapi/wasi-threads@npm:1.0.2, @emnapi/wasi-threads@npm:^1.0.2":
  version: 1.0.2
  resolution: "@emnapi/wasi-threads@npm:1.0.2"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10c0/f0621b1fc715221bd2d8332c0ca922617bcd77cdb3050eae50a124eb8923c54fa425d23982dc8f29d505c8798a62d1049bace8b0686098ff9dd82270e06d772e
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/aix-ppc64@npm:0.25.5"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/android-arm64@npm:0.25.5"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/android-arm@npm:0.25.5"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/android-x64@npm:0.25.5"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/darwin-arm64@npm:0.25.5"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/darwin-x64@npm:0.25.5"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/freebsd-arm64@npm:0.25.5"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/freebsd-x64@npm:0.25.5"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-arm64@npm:0.25.5"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-arm@npm:0.25.5"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-ia32@npm:0.25.5"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-loong64@npm:0.25.5"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-mips64el@npm:0.25.5"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-ppc64@npm:0.25.5"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-riscv64@npm:0.25.5"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-s390x@npm:0.25.5"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-x64@npm:0.25.5"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/netbsd-arm64@npm:0.25.5"
  conditions: os=netbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/netbsd-x64@npm:0.25.5"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/openbsd-arm64@npm:0.25.5"
  conditions: os=openbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/openbsd-x64@npm:0.25.5"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/sunos-x64@npm:0.25.5"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/win32-arm64@npm:0.25.5"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/win32-ia32@npm:0.25.5"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/win32-x64@npm:0.25.5"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.7.0":
  version: 4.7.0
  resolution: "@eslint-community/eslint-utils@npm:4.7.0"
  dependencies:
    eslint-visitor-keys: "npm:^3.4.3"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: 10c0/c0f4f2bd73b7b7a9de74b716a664873d08ab71ab439e51befe77d61915af41a81ecec93b408778b3a7856185244c34c2c8ee28912072ec14def84ba2dec70adf
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.10.0, @eslint-community/regexpp@npm:^4.12.1":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 10c0/a03d98c246bcb9109aec2c08e4d10c8d010256538dcb3f56610191607214523d4fb1b00aa81df830b6dffb74c5fa0be03642513a289c567949d3e550ca11cdf6
  languageName: node
  linkType: hard

"@eslint/config-array@npm:^0.20.0":
  version: 0.20.0
  resolution: "@eslint/config-array@npm:0.20.0"
  dependencies:
    "@eslint/object-schema": "npm:^2.1.6"
    debug: "npm:^4.3.1"
    minimatch: "npm:^3.1.2"
  checksum: 10c0/94bc5d0abb96dc5295ff559925242ff75a54eacfb3576677e95917e42f7175e1c4b87bf039aa2a872f949b4852ad9724bf2f7529aaea6b98f28bb3fca7f1d659
  languageName: node
  linkType: hard

"@eslint/config-helpers@npm:^0.2.1":
  version: 0.2.2
  resolution: "@eslint/config-helpers@npm:0.2.2"
  checksum: 10c0/98f7cefe484bb754674585d9e73cf1414a3ab4fd0783c385465288d13eb1a8d8e7d7b0611259fc52b76b396c11a13517be5036d1f48eeb877f6f0a6b9c4f03ad
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.14.0":
  version: 0.14.0
  resolution: "@eslint/core@npm:0.14.0"
  dependencies:
    "@types/json-schema": "npm:^7.0.15"
  checksum: 10c0/259f279445834ba2d2cbcc18e9d43202a4011fde22f29d5fb802181d66e0f6f0bd1f6b4b4b46663451f545d35134498231bd5e656e18d9034a457824b92b7741
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^3.3.1":
  version: 3.3.1
  resolution: "@eslint/eslintrc@npm:3.3.1"
  dependencies:
    ajv: "npm:^6.12.4"
    debug: "npm:^4.3.2"
    espree: "npm:^10.0.1"
    globals: "npm:^14.0.0"
    ignore: "npm:^5.2.0"
    import-fresh: "npm:^3.2.1"
    js-yaml: "npm:^4.1.0"
    minimatch: "npm:^3.1.2"
    strip-json-comments: "npm:^3.1.1"
  checksum: 10c0/b0e63f3bc5cce4555f791a4e487bf999173fcf27c65e1ab6e7d63634d8a43b33c3693e79f192cbff486d7df1be8ebb2bd2edc6e70ddd486cbfa84a359a3e3b41
  languageName: node
  linkType: hard

"@eslint/js@npm:9.28.0, @eslint/js@npm:^9.21.0":
  version: 9.28.0
  resolution: "@eslint/js@npm:9.28.0"
  checksum: 10c0/5a6759542490dd9f778993edfbc8d2f55168fd0f7336ceed20fe3870c65499d72fc0bca8d1ae00ea246b0923ea4cba2e0758a8a5507a3506ddcf41c92282abb8
  languageName: node
  linkType: hard

"@eslint/object-schema@npm:^2.1.6":
  version: 2.1.6
  resolution: "@eslint/object-schema@npm:2.1.6"
  checksum: 10c0/b8cdb7edea5bc5f6a96173f8d768d3554a628327af536da2fc6967a93b040f2557114d98dbcdbf389d5a7b290985ad6a9ce5babc547f36fc1fde42e674d11a56
  languageName: node
  linkType: hard

"@eslint/plugin-kit@npm:^0.3.1":
  version: 0.3.1
  resolution: "@eslint/plugin-kit@npm:0.3.1"
  dependencies:
    "@eslint/core": "npm:^0.14.0"
    levn: "npm:^0.4.1"
  checksum: 10c0/a75f0b5d38430318a551b83e27bee570747eb50beeb76b03f64b0e78c2c27ef3d284cfda3443134df028db3251719bc0850c105f778122f6ad762d5270ec8063
  languageName: node
  linkType: hard

"@floating-ui/core@npm:^1.7.1":
  version: 1.7.1
  resolution: "@floating-ui/core@npm:1.7.1"
  dependencies:
    "@floating-ui/utils": "npm:^0.2.9"
  checksum: 10c0/40df1e1dd8a2bad6f70c1ee163f0e151c456f52b9b98a38488d88720b2be72ccd631501a66f8369f96d2e8ad1c4250936b6fd4243e3a99833f2d008ee6afec18
  languageName: node
  linkType: hard

"@floating-ui/dom@npm:^1.0.0":
  version: 1.7.1
  resolution: "@floating-ui/dom@npm:1.7.1"
  dependencies:
    "@floating-ui/core": "npm:^1.7.1"
    "@floating-ui/utils": "npm:^0.2.9"
  checksum: 10c0/33b0e892f4c50ce568169cd58793ff5e3bc1e72ee007237d73b9458d4475e1e5f5a4b3f9e6752422d5f5ac902bc0c135ca7dc0a23c6df187fd9d28dc34cdceed
  languageName: node
  linkType: hard

"@floating-ui/react-dom@npm:^2.0.0":
  version: 2.1.3
  resolution: "@floating-ui/react-dom@npm:2.1.3"
  dependencies:
    "@floating-ui/dom": "npm:^1.0.0"
  peerDependencies:
    react: ">=16.8.0"
    react-dom: ">=16.8.0"
  checksum: 10c0/e88750ea2fb352264d52d502d3979f94155ce2c8ab9a50862810d0cfc8c8e49cb6bbde466d668736cb38624d089360ef97451397b647408a0eb2c1870234c19a
  languageName: node
  linkType: hard

"@floating-ui/utils@npm:^0.2.9":
  version: 0.2.9
  resolution: "@floating-ui/utils@npm:0.2.9"
  checksum: 10c0/48bbed10f91cb7863a796cc0d0e917c78d11aeb89f98d03fc38d79e7eb792224a79f538ed8a2d5d5584511d4ca6354ef35f1712659fd569868e342df4398ad6f
  languageName: node
  linkType: hard

"@google/generative-ai@npm:^0.21.0":
  version: 0.21.0
  resolution: "@google/generative-ai@npm:0.21.0"
  checksum: 10c0/cff5946c5964f2380e5097d82bd563d79be27a1a5ac604aaaad3f9ba3382992e4f0a371bd255baabfba4e5bdf296d8ce1410cbd65424afa98e64b2590fe49f3b
  languageName: node
  linkType: hard

"@huggingface/hub@npm:^1.1.1":
  version: 1.1.2
  resolution: "@huggingface/hub@npm:1.1.2"
  dependencies:
    "@huggingface/tasks": "npm:^0.18.2"
  checksum: 10c0/7403a93a343778cca0a160d67486d2d4bdefa5f4a9d52f54fbf4d0b8d6d5fb2d64f8459ceddac062d3758c66e94acd1c098aa870b97ad6c503d2d7beed922b16
  languageName: node
  linkType: hard

"@huggingface/inference@npm:^4.0.2":
  version: 4.0.2
  resolution: "@huggingface/inference@npm:4.0.2"
  dependencies:
    "@huggingface/jinja": "npm:^0.5.0"
    "@huggingface/tasks": "npm:^0.19.11"
  checksum: 10c0/b9a496d9a596d475ebda72d91baca0c324dbd6e04fe53368f7741d6fc5eaee63eb6970e9db056714859e7d8f23a85db400ba4366cfb38608be6031b60479b524
  languageName: node
  linkType: hard

"@huggingface/jinja@npm:^0.2.2":
  version: 0.2.2
  resolution: "@huggingface/jinja@npm:0.2.2"
  checksum: 10c0/b6c1933e2dd8f9fbbdd79cf57a23cecba7a63b6fba72eb978ca11386b10d846183a0e4d245921fe969cbc1398518bfcde284979ebecc3b6859ec73a6a25176d6
  languageName: node
  linkType: hard

"@huggingface/jinja@npm:^0.5.0":
  version: 0.5.0
  resolution: "@huggingface/jinja@npm:0.5.0"
  checksum: 10c0/76511b07d03aa2c0f0b9a860be44fbcdc287dcae826cf32b9b3c9b0a5ed300f1b7c76dbc07b297808a3497dddc426058160a274fb6db9c54d1f89c5ceb295d61
  languageName: node
  linkType: hard

"@huggingface/tasks@npm:^0.18.2":
  version: 0.18.12
  resolution: "@huggingface/tasks@npm:0.18.12"
  checksum: 10c0/30f814e473d02d0244770066712789396a87874d2f2d645d69e4d7aa61d09e47fca5e5f7998915390689d8ea8837b34f517ffc2ba4fc397c731236aff1350436
  languageName: node
  linkType: hard

"@huggingface/tasks@npm:^0.19.11":
  version: 0.19.11
  resolution: "@huggingface/tasks@npm:0.19.11"
  checksum: 10c0/c9f457c7fed02fd9ea129b3b87c62e97613197030bff39c068ecc854efbd2dd74161b63a7fee329492ad9a20882593947f44a828ed14a7ce8fc04fdc88d042f4
  languageName: node
  linkType: hard

"@humanfs/core@npm:^0.19.1":
  version: 0.19.1
  resolution: "@humanfs/core@npm:0.19.1"
  checksum: 10c0/aa4e0152171c07879b458d0e8a704b8c3a89a8c0541726c6b65b81e84fd8b7564b5d6c633feadc6598307d34564bd53294b533491424e8e313d7ab6c7bc5dc67
  languageName: node
  linkType: hard

"@humanfs/node@npm:^0.16.6":
  version: 0.16.6
  resolution: "@humanfs/node@npm:0.16.6"
  dependencies:
    "@humanfs/core": "npm:^0.19.1"
    "@humanwhocodes/retry": "npm:^0.3.0"
  checksum: 10c0/8356359c9f60108ec204cbd249ecd0356667359b2524886b357617c4a7c3b6aace0fd5a369f63747b926a762a88f8a25bc066fa1778508d110195ce7686243e1
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 10c0/909b69c3b86d482c26b3359db16e46a32e0fb30bd306a3c176b8313b9e7313dba0f37f519de6aa8b0a1921349e505f259d19475e123182416a506d7f87e7f529
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.3.0":
  version: 0.3.1
  resolution: "@humanwhocodes/retry@npm:0.3.1"
  checksum: 10c0/f0da1282dfb45e8120480b9e2e275e2ac9bbe1cf016d046fdad8e27cc1285c45bb9e711681237944445157b430093412b4446c1ab3fc4bb037861b5904101d3b
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.4.2":
  version: 0.4.3
  resolution: "@humanwhocodes/retry@npm:0.4.3"
  checksum: 10c0/3775bb30087d4440b3f7406d5a057777d90e4b9f435af488a4923ef249e93615fb78565a85f173a186a076c7706a81d0d57d563a2624e4de2c5c9c66c486ce42
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10c0/b1bf42535d49f11dc137f18d5e4e63a28c5569de438a221c369483731e9dac9fb797af554e8bf02b6192d1e5eba6e6402cf93900c3d0ac86391d00d04876789e
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10c0/c25b6dc1598790d5b55c0947a9b7d111cfa92594db5296c3b907e2f533c033666f692a3939eadac17b1c7c40d362d0b0635dc874cbfe3e70db7c2b07cc97a5d2
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.8
  resolution: "@jridgewell/gen-mapping@npm:0.3.8"
  dependencies:
    "@jridgewell/set-array": "npm:^1.2.1"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/c668feaf86c501d7c804904a61c23c67447b2137b813b9ce03eca82cb9d65ac7006d766c218685d76e3d72828279b6ee26c347aa1119dab23fbaf36aed51585a
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 10c0/d502e6fb516b35032331406d4e962c21fe77cdf1cbdb49c6142bcbd9e30507094b18972778a6e27cbad756209cfe34b1a27729e6fa08a2eb92b33943f680cf1e
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 10c0/2a5aa7b4b5c3464c895c802d8ae3f3d2b92fcbe84ad12f8d0bfbb1f5ad006717e7577ee1fd2eac00c088abe486c7adb27976f45d2941ff6b0b92b2c3302c60f4
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14, @jridgewell/sourcemap-codec@npm:^1.4.15, @jridgewell/sourcemap-codec@npm:^1.5.0":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 10c0/2eb864f276eb1096c3c11da3e9bb518f6d9fc0023c78344cdc037abadc725172c70314bdb360f2d4b7bffec7f5d657ce006816bc5d4ecb35e61b66132db00c18
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 10c0/3d1ce6ebc69df9682a5a8896b414c6537e428a1d68b02fcc8363b04284a8ca0df04d0ee3013132252ab14f2527bc13bea6526a912ecb5658f0e39fd2860b4df4
  languageName: node
  linkType: hard

"@langchain/anthropic@npm:^0.3.21":
  version: 0.3.21
  resolution: "@langchain/anthropic@npm:0.3.21"
  dependencies:
    "@anthropic-ai/sdk": "npm:^0.39.0"
    fast-xml-parser: "npm:^4.4.1"
    zod: "npm:^3.22.4"
    zod-to-json-schema: "npm:^3.22.4"
  peerDependencies:
    "@langchain/core": ">=0.3.48 <0.4.0"
  checksum: 10c0/04e7f25bf6e0775b58b93a33c7931284340724c333690c1de645c22edc32dd2705ddb492be3700bd99ca941c4f61bb81f898c5044ebe7a7f2b620710b034f431
  languageName: node
  linkType: hard

"@langchain/core@npm:^0.3.57":
  version: 0.3.57
  resolution: "@langchain/core@npm:0.3.57"
  dependencies:
    "@cfworker/json-schema": "npm:^4.0.2"
    ansi-styles: "npm:^5.0.0"
    camelcase: "npm:6"
    decamelize: "npm:1.2.0"
    js-tiktoken: "npm:^1.0.12"
    langsmith: "npm:^0.3.29"
    mustache: "npm:^4.2.0"
    p-queue: "npm:^6.6.2"
    p-retry: "npm:4"
    uuid: "npm:^10.0.0"
    zod: "npm:^3.22.4"
    zod-to-json-schema: "npm:^3.22.3"
  checksum: 10c0/70e46a9ce85c1cf0fe7912d6b43be8eb57f50423b147f2cada3276a34ea99542e08c06f7c38e334dbd4e91265b40fbfa78c2708bc8c8b3b4e6a82d6deafcdb56
  languageName: node
  linkType: hard

"@langchain/langgraph-checkpoint@npm:~0.0.18":
  version: 0.0.18
  resolution: "@langchain/langgraph-checkpoint@npm:0.0.18"
  dependencies:
    uuid: "npm:^10.0.0"
  peerDependencies:
    "@langchain/core": ">=0.2.31 <0.4.0"
  checksum: 10c0/d0a1525a7e45044953ddaafeeb702f7ddfb3d30632ac93842f8f940b1fe410f6e132080db6f6843e0a19bc59f8e4fef6b55d59f2812bc708f50cbec5fdcd6ce6
  languageName: node
  linkType: hard

"@langchain/langgraph-sdk@npm:~0.0.32":
  version: 0.0.83
  resolution: "@langchain/langgraph-sdk@npm:0.0.83"
  dependencies:
    "@types/json-schema": "npm:^7.0.15"
    p-queue: "npm:^6.6.2"
    p-retry: "npm:4"
    uuid: "npm:^9.0.0"
  peerDependencies:
    "@langchain/core": ">=0.2.31 <0.4.0"
    react: ^18 || ^19
  peerDependenciesMeta:
    "@langchain/core":
      optional: true
    react:
      optional: true
  checksum: 10c0/889f2e1740d1844b8aba77edaf8f78234e7bdbf1e85f769f653ba64865b1ee2940ab63daa09d59d05903797e02fc8499d0ebd9c3a8ca42bab79848f4cc06efa3
  languageName: node
  linkType: hard

"@langchain/langgraph@npm:^0.3.1":
  version: 0.3.1
  resolution: "@langchain/langgraph@npm:0.3.1"
  dependencies:
    "@langchain/langgraph-checkpoint": "npm:~0.0.18"
    "@langchain/langgraph-sdk": "npm:~0.0.32"
    uuid: "npm:^10.0.0"
    zod: "npm:^3.23.8"
  peerDependencies:
    "@langchain/core": ">=0.2.36 <0.3.0 || >=0.3.40 < 0.4.0"
    zod-to-json-schema: ^3.x
  peerDependenciesMeta:
    zod-to-json-schema:
      optional: true
  checksum: 10c0/ac4a8a8c3cad0b76cdd9d824ae7cce82b8cd2608fb50a37fcd2f5ead58452884ff2a12e2bd77f05d5dcad64c6dbdb6b0ffc978926c0e1af8005b539877158a5e
  languageName: node
  linkType: hard

"@langchain/openai@npm:^0.5.12":
  version: 0.5.12
  resolution: "@langchain/openai@npm:0.5.12"
  dependencies:
    js-tiktoken: "npm:^1.0.12"
    openai: "npm:^4.96.0"
    zod: "npm:^3.22.4"
    zod-to-json-schema: "npm:^3.22.3"
  peerDependencies:
    "@langchain/core": ">=0.3.48 <0.4.0"
  checksum: 10c0/1c24fae11d8b693e841c868af33d8abc484f93c0a11b033261635abb57035bf9aa344b75d20489dfe85abb8762cf0c26bc8aa8ede37c665ccac5088a04bf9c84
  languageName: node
  linkType: hard

"@monaco-editor/loader@npm:^1.5.0":
  version: 1.5.0
  resolution: "@monaco-editor/loader@npm:1.5.0"
  dependencies:
    state-local: "npm:^1.0.6"
  checksum: 10c0/d8e1fd75fea113b4d91405784ed4db757df450c6e57a85e87728281e819a73a3ef9fe0cbb1dc1509456f1e895ad9c11e4748f80b68900cb6cf600170e14ce6ed
  languageName: node
  linkType: hard

"@monaco-editor/react@npm:^4.7.0":
  version: 4.7.0
  resolution: "@monaco-editor/react@npm:4.7.0"
  dependencies:
    "@monaco-editor/loader": "npm:^1.5.0"
  peerDependencies:
    monaco-editor: ">= 0.25.0 < 1"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10c0/a4e91c6eda1a71f5fd23871bd801de435490ccbc43934d23cba65cefe2be7e9d02c9a57c4ef80fc9fe99e4d4deea51e5db19cb4e0ecf3f51818dda0eb9cdbf12
  languageName: node
  linkType: hard

"@napi-rs/wasm-runtime@npm:^0.2.10":
  version: 0.2.10
  resolution: "@napi-rs/wasm-runtime@npm:0.2.10"
  dependencies:
    "@emnapi/core": "npm:^1.4.3"
    "@emnapi/runtime": "npm:^1.4.3"
    "@tybys/wasm-util": "npm:^0.9.0"
  checksum: 10c0/4dce9bbb94a8969805574e1b55fdbeb7623348190265d77f6507ba32e535610deeb53a33ba0bb8b05a6520f379d418b92e8a01c5cd7b9486b136d2c0c26be0bd
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10c0/732c3b6d1b1e967440e65f284bd06e5821fedf10a1bea9ed2bb75956ea1f30e08c44d3def9d6a230666574edbaf136f8cfd319c14fd1f87c66e6a44449afb2eb
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10c0/88dafe5e3e29a388b07264680dc996c17f4bda48d163a9d4f5c1112979f0ce8ec72aa7116122c350b4e7976bc5566dc3ddb579be1ceaacc727872eb4ed93926d
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10c0/db9de047c3bb9b51f9335a7bb46f4fcfb6829fb628318c12115fbaf7d369bfce71c15b103d1fc3b464812d936220ee9bc1c8f762d032c9f6be9acc99249095b1
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10c0/efe37b982f30740ee77696a80c196912c274ecd2cb243bc6ae7053a50c733ce0f6c09fda085145f33ecf453be19654acca74b69e81eaad4c90f00ccffe2f9271
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10c0/c90935d5ce670c87b6b14fab04a965a3b8137e585f8b2a6257263bd7f97756dd736cb165bb470e5156a9e718ecd99413dccc54b1138c1a46d6ec7cf325982fe5
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10c0/5bd7576bb1b38a47a7fc7b51ac9f38748e772beebc56200450c4a817d712232b8f1d3ef70532c80840243c657d491cf6a6be1e3a214cff907645819fdc34aadd
  languageName: node
  linkType: hard

"@protobufjs/aspromise@npm:^1.1.1, @protobufjs/aspromise@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/aspromise@npm:1.1.2"
  checksum: 10c0/a83343a468ff5b5ec6bff36fd788a64c839e48a07ff9f4f813564f58caf44d011cd6504ed2147bf34835bd7a7dd2107052af755961c6b098fd8902b4f6500d0f
  languageName: node
  linkType: hard

"@protobufjs/base64@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/base64@npm:1.1.2"
  checksum: 10c0/eec925e681081af190b8ee231f9bad3101e189abbc182ff279da6b531e7dbd2a56f1f306f37a80b1be9e00aa2d271690d08dcc5f326f71c9eed8546675c8caf6
  languageName: node
  linkType: hard

"@protobufjs/codegen@npm:^2.0.4":
  version: 2.0.4
  resolution: "@protobufjs/codegen@npm:2.0.4"
  checksum: 10c0/26ae337c5659e41f091606d16465bbcc1df1f37cc1ed462438b1f67be0c1e28dfb2ca9f294f39100c52161aef82edf758c95d6d75650a1ddf31f7ddee1440b43
  languageName: node
  linkType: hard

"@protobufjs/eventemitter@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/eventemitter@npm:1.1.0"
  checksum: 10c0/1eb0a75180e5206d1033e4138212a8c7089a3d418c6dfa5a6ce42e593a4ae2e5892c4ef7421f38092badba4040ea6a45f0928869989411001d8c1018ea9a6e70
  languageName: node
  linkType: hard

"@protobufjs/fetch@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/fetch@npm:1.1.0"
  dependencies:
    "@protobufjs/aspromise": "npm:^1.1.1"
    "@protobufjs/inquire": "npm:^1.1.0"
  checksum: 10c0/cda6a3dc2d50a182c5865b160f72077aac197046600091dbb005dd0a66db9cce3c5eaed6d470ac8ed49d7bcbeef6ee5f0bc288db5ff9a70cbd003e5909065233
  languageName: node
  linkType: hard

"@protobufjs/float@npm:^1.0.2":
  version: 1.0.2
  resolution: "@protobufjs/float@npm:1.0.2"
  checksum: 10c0/18f2bdede76ffcf0170708af15c9c9db6259b771e6b84c51b06df34a9c339dbbeec267d14ce0bddd20acc142b1d980d983d31434398df7f98eb0c94a0eb79069
  languageName: node
  linkType: hard

"@protobufjs/inquire@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/inquire@npm:1.1.0"
  checksum: 10c0/64372482efcba1fb4d166a2664a6395fa978b557803857c9c03500e0ac1013eb4b1aacc9ed851dd5fc22f81583670b4f4431bae186f3373fedcfde863ef5921a
  languageName: node
  linkType: hard

"@protobufjs/path@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/path@npm:1.1.2"
  checksum: 10c0/cece0a938e7f5dfd2fa03f8c14f2f1cf8b0d6e13ac7326ff4c96ea311effd5fb7ae0bba754fbf505312af2e38500250c90e68506b97c02360a43793d88a0d8b4
  languageName: node
  linkType: hard

"@protobufjs/pool@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/pool@npm:1.1.0"
  checksum: 10c0/eda2718b7f222ac6e6ad36f758a92ef90d26526026a19f4f17f668f45e0306a5bd734def3f48f51f8134ae0978b6262a5c517c08b115a551756d1a3aadfcf038
  languageName: node
  linkType: hard

"@protobufjs/utf8@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/utf8@npm:1.1.0"
  checksum: 10c0/a3fe31fe3fa29aa3349e2e04ee13dc170cc6af7c23d92ad49e3eeaf79b9766264544d3da824dba93b7855bd6a2982fb40032ef40693da98a136d835752beb487
  languageName: node
  linkType: hard

"@radix-ui/number@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/number@npm:1.1.1"
  checksum: 10c0/0570ad92287398e8a7910786d7cee0a998174cdd6637ba61571992897c13204adf70b9ed02d0da2af554119411128e701d9c6b893420612897b438dc91db712b
  languageName: node
  linkType: hard

"@radix-ui/primitive@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/primitive@npm:1.1.2"
  checksum: 10c0/5e2d2528d2fe37c16865e77b0beaac2b415a817ad13d8178db6e8187b2a092672568a64ee0041510abfde3034490a5cadd3057049bb15789020c06892047597c
  languageName: node
  linkType: hard

"@radix-ui/react-arrow@npm:1.1.7":
  version: 1.1.7
  resolution: "@radix-ui/react-arrow@npm:1.1.7"
  dependencies:
    "@radix-ui/react-primitive": "npm:2.1.3"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/c3b46766238b3ee2a394d8806a5141432361bf1425110c9f0dcf480bda4ebd304453a53f294b5399c6ee3ccfcae6fd544921fd01ddc379cf5942acdd7168664b
  languageName: node
  linkType: hard

"@radix-ui/react-avatar@npm:^1.1.10":
  version: 1.1.10
  resolution: "@radix-ui/react-avatar@npm:1.1.10"
  dependencies:
    "@radix-ui/react-context": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.1.3"
    "@radix-ui/react-use-callback-ref": "npm:1.1.1"
    "@radix-ui/react-use-is-hydrated": "npm:0.1.0"
    "@radix-ui/react-use-layout-effect": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/9fb0cf9a9d0fdbeaa2efda476402fc09db2e6ff9cd9aa3ea1d315d9c9579840722a4833725cb196c455e0bd775dfe04221a4f6855685ce89d2133c42e2b07e5f
  languageName: node
  linkType: hard

"@radix-ui/react-collection@npm:1.1.7":
  version: 1.1.7
  resolution: "@radix-ui/react-collection@npm:1.1.7"
  dependencies:
    "@radix-ui/react-compose-refs": "npm:1.1.2"
    "@radix-ui/react-context": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.1.3"
    "@radix-ui/react-slot": "npm:1.2.3"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/fa321a7300095508491f75414f02b243f0c3f179dc0728cfd115e2ea9f6f48f1516532b59f526d9ac81bbab63cd98a052074b4703ec0b9428fac945ebabec5fd
  languageName: node
  linkType: hard

"@radix-ui/react-compose-refs@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/react-compose-refs@npm:1.1.2"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/d36a9c589eb75d634b9b139c80f916aadaf8a68a7c1c4b8c6c6b88755af1a92f2e343457042089f04cc3f23073619d08bb65419ced1402e9d4e299576d970771
  languageName: node
  linkType: hard

"@radix-ui/react-context@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/react-context@npm:1.1.2"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/cece731f8cc25d494c6589cc681e5c01a93867d895c75889973afa1a255f163c286e390baa7bc028858eaabe9f6b57270d0ca6377356f652c5557c1c7a41ccce
  languageName: node
  linkType: hard

"@radix-ui/react-dialog@npm:^1.1.14":
  version: 1.1.14
  resolution: "@radix-ui/react-dialog@npm:1.1.14"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.2"
    "@radix-ui/react-compose-refs": "npm:1.1.2"
    "@radix-ui/react-context": "npm:1.1.2"
    "@radix-ui/react-dismissable-layer": "npm:1.1.10"
    "@radix-ui/react-focus-guards": "npm:1.1.2"
    "@radix-ui/react-focus-scope": "npm:1.1.7"
    "@radix-ui/react-id": "npm:1.1.1"
    "@radix-ui/react-portal": "npm:1.1.9"
    "@radix-ui/react-presence": "npm:1.1.4"
    "@radix-ui/react-primitive": "npm:2.1.3"
    "@radix-ui/react-slot": "npm:1.2.3"
    "@radix-ui/react-use-controllable-state": "npm:1.2.2"
    aria-hidden: "npm:^1.2.4"
    react-remove-scroll: "npm:^2.6.3"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/ab7bc783510ed8fccfe91020b214f4a571d5a1d46d398faa33f4c151bc9f586c47483b307e72b67687b06694c194b3aa80dd1de728460fa765db9f3057690ba3
  languageName: node
  linkType: hard

"@radix-ui/react-direction@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-direction@npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/7a89d9291f846a3105e45f4df98d6b7a08f8d7b30acdcd253005dc9db107ee83cbbebc9e47a9af1e400bcd47697f1511ceab23a399b0da854488fc7220482ac9
  languageName: node
  linkType: hard

"@radix-ui/react-dismissable-layer@npm:1.1.10":
  version: 1.1.10
  resolution: "@radix-ui/react-dismissable-layer@npm:1.1.10"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.2"
    "@radix-ui/react-compose-refs": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.1.3"
    "@radix-ui/react-use-callback-ref": "npm:1.1.1"
    "@radix-ui/react-use-escape-keydown": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/21a2d03689f5e06586135b6a735937ef14f2571fdf6044a3019bc3f9fa368a9400b5a9b631f43e8ad3682693449e369ffa7cc8642764246ce18ebe7359a45faf
  languageName: node
  linkType: hard

"@radix-ui/react-dropdown-menu@npm:^2.1.15":
  version: 2.1.15
  resolution: "@radix-ui/react-dropdown-menu@npm:2.1.15"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.2"
    "@radix-ui/react-compose-refs": "npm:1.1.2"
    "@radix-ui/react-context": "npm:1.1.2"
    "@radix-ui/react-id": "npm:1.1.1"
    "@radix-ui/react-menu": "npm:2.1.15"
    "@radix-ui/react-primitive": "npm:2.1.3"
    "@radix-ui/react-use-controllable-state": "npm:1.2.2"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/ea5e7c98d38e7a2e66e2a77057471f1309f414dc0013042241d33e09e1d7c3844908f845d47bd34cd5ae78d344973b381b8e5de890e7546d799c1960740b24b6
  languageName: node
  linkType: hard

"@radix-ui/react-focus-guards@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/react-focus-guards@npm:1.1.2"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/8d6fa55752b9b6e55d1eebb643178e38a824e8ba418eb29031b2979077a12c4e3922892de9f984dd326f77071a14960cd81e99a960beea07598b8c80da618dc5
  languageName: node
  linkType: hard

"@radix-ui/react-focus-scope@npm:1.1.7":
  version: 1.1.7
  resolution: "@radix-ui/react-focus-scope@npm:1.1.7"
  dependencies:
    "@radix-ui/react-compose-refs": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.1.3"
    "@radix-ui/react-use-callback-ref": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/8a6071331bdeeb79b223463de75caf759b8ad19339cab838e537b8dbb2db236891a1f4df252445c854d375d43d9d315dfcce0a6b01553a2984ec372bb8f1300e
  languageName: node
  linkType: hard

"@radix-ui/react-id@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-id@npm:1.1.1"
  dependencies:
    "@radix-ui/react-use-layout-effect": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/7d12e76818763d592c331277ef62b197e2e64945307e650bd058f0090e5ae48bbd07691b23b7e9e977901ef4eadcb3e2d5eaeb17a13859083384be83fc1292c7
  languageName: node
  linkType: hard

"@radix-ui/react-menu@npm:2.1.15":
  version: 2.1.15
  resolution: "@radix-ui/react-menu@npm:2.1.15"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.2"
    "@radix-ui/react-collection": "npm:1.1.7"
    "@radix-ui/react-compose-refs": "npm:1.1.2"
    "@radix-ui/react-context": "npm:1.1.2"
    "@radix-ui/react-direction": "npm:1.1.1"
    "@radix-ui/react-dismissable-layer": "npm:1.1.10"
    "@radix-ui/react-focus-guards": "npm:1.1.2"
    "@radix-ui/react-focus-scope": "npm:1.1.7"
    "@radix-ui/react-id": "npm:1.1.1"
    "@radix-ui/react-popper": "npm:1.2.7"
    "@radix-ui/react-portal": "npm:1.1.9"
    "@radix-ui/react-presence": "npm:1.1.4"
    "@radix-ui/react-primitive": "npm:2.1.3"
    "@radix-ui/react-roving-focus": "npm:1.1.10"
    "@radix-ui/react-slot": "npm:1.2.3"
    "@radix-ui/react-use-callback-ref": "npm:1.1.1"
    aria-hidden: "npm:^1.2.4"
    react-remove-scroll: "npm:^2.6.3"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/09306b1856448d0310fdcb732c159c7d1bddd0d2da6706c1567e0218f277597d8203b1138107a40665620bea397c15ec6e353295dfc5752a45c08daf552ad533
  languageName: node
  linkType: hard

"@radix-ui/react-popover@npm:^1.1.14":
  version: 1.1.14
  resolution: "@radix-ui/react-popover@npm:1.1.14"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.2"
    "@radix-ui/react-compose-refs": "npm:1.1.2"
    "@radix-ui/react-context": "npm:1.1.2"
    "@radix-ui/react-dismissable-layer": "npm:1.1.10"
    "@radix-ui/react-focus-guards": "npm:1.1.2"
    "@radix-ui/react-focus-scope": "npm:1.1.7"
    "@radix-ui/react-id": "npm:1.1.1"
    "@radix-ui/react-popper": "npm:1.2.7"
    "@radix-ui/react-portal": "npm:1.1.9"
    "@radix-ui/react-presence": "npm:1.1.4"
    "@radix-ui/react-primitive": "npm:2.1.3"
    "@radix-ui/react-slot": "npm:1.2.3"
    "@radix-ui/react-use-controllable-state": "npm:1.2.2"
    aria-hidden: "npm:^1.2.4"
    react-remove-scroll: "npm:^2.6.3"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/04e557bfcaab4887694d119555b101e16b8a4e99595541ff2cbe805c551be853cb02882a2ada04e6507ffc45bc092bc2b89704b7b79f5025251767d0b4f3230a
  languageName: node
  linkType: hard

"@radix-ui/react-popper@npm:1.2.7":
  version: 1.2.7
  resolution: "@radix-ui/react-popper@npm:1.2.7"
  dependencies:
    "@floating-ui/react-dom": "npm:^2.0.0"
    "@radix-ui/react-arrow": "npm:1.1.7"
    "@radix-ui/react-compose-refs": "npm:1.1.2"
    "@radix-ui/react-context": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.1.3"
    "@radix-ui/react-use-callback-ref": "npm:1.1.1"
    "@radix-ui/react-use-layout-effect": "npm:1.1.1"
    "@radix-ui/react-use-rect": "npm:1.1.1"
    "@radix-ui/react-use-size": "npm:1.1.1"
    "@radix-ui/rect": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/fb901329df5432225b0be08778a89faaa25c40e8042f0f181218e385cae26811420b6e4b1effc70955393e09d83cd462d1b0eb6ca6d33282d76692972b602ad8
  languageName: node
  linkType: hard

"@radix-ui/react-portal@npm:1.1.9":
  version: 1.1.9
  resolution: "@radix-ui/react-portal@npm:1.1.9"
  dependencies:
    "@radix-ui/react-primitive": "npm:2.1.3"
    "@radix-ui/react-use-layout-effect": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/45b432497c722720c72c493a29ef6085bc84b50eafe79d48b45c553121b63e94f9cdb77a3a74b9c49126f8feb3feee009fe400d48b7759d3552396356b192cd7
  languageName: node
  linkType: hard

"@radix-ui/react-presence@npm:1.1.4":
  version: 1.1.4
  resolution: "@radix-ui/react-presence@npm:1.1.4"
  dependencies:
    "@radix-ui/react-compose-refs": "npm:1.1.2"
    "@radix-ui/react-use-layout-effect": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/8202647139d6f5097b0abcc43dfba471c00b69da95ca336afe3ea23a165e05ca21992f40fc801760fe442f3e064e54e2f2cbcb9ad758c4b07ef6c69a5b6777bd
  languageName: node
  linkType: hard

"@radix-ui/react-primitive@npm:2.1.3":
  version: 2.1.3
  resolution: "@radix-ui/react-primitive@npm:2.1.3"
  dependencies:
    "@radix-ui/react-slot": "npm:1.2.3"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/fdff9b84913bb4172ef6d3af7442fca5f9bba5f2709cba08950071f819d7057aec3a4a2d9ef44cf9cbfb8014d02573c6884a04cff175895823aaef809ebdb034
  languageName: node
  linkType: hard

"@radix-ui/react-roving-focus@npm:1.1.10":
  version: 1.1.10
  resolution: "@radix-ui/react-roving-focus@npm:1.1.10"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.2"
    "@radix-ui/react-collection": "npm:1.1.7"
    "@radix-ui/react-compose-refs": "npm:1.1.2"
    "@radix-ui/react-context": "npm:1.1.2"
    "@radix-ui/react-direction": "npm:1.1.1"
    "@radix-ui/react-id": "npm:1.1.1"
    "@radix-ui/react-primitive": "npm:2.1.3"
    "@radix-ui/react-use-callback-ref": "npm:1.1.1"
    "@radix-ui/react-use-controllable-state": "npm:1.2.2"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/afc8faed4d43807cb6b9e163995f5224fc11d4480aa8033274c858a2fc01f04d190e9fbf209db21be4f6d6f48689698ab900a8bd39e453ef55d00d58c2b840bb
  languageName: node
  linkType: hard

"@radix-ui/react-select@npm:^2.2.5":
  version: 2.2.5
  resolution: "@radix-ui/react-select@npm:2.2.5"
  dependencies:
    "@radix-ui/number": "npm:1.1.1"
    "@radix-ui/primitive": "npm:1.1.2"
    "@radix-ui/react-collection": "npm:1.1.7"
    "@radix-ui/react-compose-refs": "npm:1.1.2"
    "@radix-ui/react-context": "npm:1.1.2"
    "@radix-ui/react-direction": "npm:1.1.1"
    "@radix-ui/react-dismissable-layer": "npm:1.1.10"
    "@radix-ui/react-focus-guards": "npm:1.1.2"
    "@radix-ui/react-focus-scope": "npm:1.1.7"
    "@radix-ui/react-id": "npm:1.1.1"
    "@radix-ui/react-popper": "npm:1.2.7"
    "@radix-ui/react-portal": "npm:1.1.9"
    "@radix-ui/react-primitive": "npm:2.1.3"
    "@radix-ui/react-slot": "npm:1.2.3"
    "@radix-ui/react-use-callback-ref": "npm:1.1.1"
    "@radix-ui/react-use-controllable-state": "npm:1.2.2"
    "@radix-ui/react-use-layout-effect": "npm:1.1.1"
    "@radix-ui/react-use-previous": "npm:1.1.1"
    "@radix-ui/react-visually-hidden": "npm:1.2.3"
    aria-hidden: "npm:^1.2.4"
    react-remove-scroll: "npm:^2.6.3"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/9571a210b969446a290022447dc7bcfa5bdd90fa18b5e1ace03482d423cd75303d2353b17187b1110d93bc06efa11467f369c3f22a9daab34973c16b64547f27
  languageName: node
  linkType: hard

"@radix-ui/react-slot@npm:1.2.3, @radix-ui/react-slot@npm:^1.2.3":
  version: 1.2.3
  resolution: "@radix-ui/react-slot@npm:1.2.3"
  dependencies:
    "@radix-ui/react-compose-refs": "npm:1.1.2"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/5913aa0d760f505905779515e4b1f0f71a422350f077cc8d26d1aafe53c97f177fec0e6d7fbbb50d8b5e498aa9df9f707ca75ae3801540c283b26b0136138eef
  languageName: node
  linkType: hard

"@radix-ui/react-switch@npm:^1.2.5":
  version: 1.2.5
  resolution: "@radix-ui/react-switch@npm:1.2.5"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.2"
    "@radix-ui/react-compose-refs": "npm:1.1.2"
    "@radix-ui/react-context": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.1.3"
    "@radix-ui/react-use-controllable-state": "npm:1.2.2"
    "@radix-ui/react-use-previous": "npm:1.1.1"
    "@radix-ui/react-use-size": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/7149649b852adaba5a65d6ac865c0f33ba7ec8282c72eb17286df9e4b67d4bf1eaa7eb3b4ceeb64c5ecea65fe439b7ed6c9d9829d5baa2b757b8db630e7b87ff
  languageName: node
  linkType: hard

"@radix-ui/react-tabs@npm:^1.1.12":
  version: 1.1.12
  resolution: "@radix-ui/react-tabs@npm:1.1.12"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.2"
    "@radix-ui/react-context": "npm:1.1.2"
    "@radix-ui/react-direction": "npm:1.1.1"
    "@radix-ui/react-id": "npm:1.1.1"
    "@radix-ui/react-presence": "npm:1.1.4"
    "@radix-ui/react-primitive": "npm:2.1.3"
    "@radix-ui/react-roving-focus": "npm:1.1.10"
    "@radix-ui/react-use-controllable-state": "npm:1.2.2"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/ca171a3170d746fe2c72cdbda4332a0267f887545142a98befa21895ff7198f0541405ece55c701ef4c06bdf45c92c01bf07a554eb57c2f7a1d972f47c636495
  languageName: node
  linkType: hard

"@radix-ui/react-toggle-group@npm:^1.1.10":
  version: 1.1.10
  resolution: "@radix-ui/react-toggle-group@npm:1.1.10"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.2"
    "@radix-ui/react-context": "npm:1.1.2"
    "@radix-ui/react-direction": "npm:1.1.1"
    "@radix-ui/react-primitive": "npm:2.1.3"
    "@radix-ui/react-roving-focus": "npm:1.1.10"
    "@radix-ui/react-toggle": "npm:1.1.9"
    "@radix-ui/react-use-controllable-state": "npm:1.2.2"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/99df4d3ef45e45c08c1a5c56e1980178f2647b188d003159b49eeb900ceacf760d8c4113a61a3dc6e74e2295586666aed93a7d7f9fa63c0705447bbcc023789e
  languageName: node
  linkType: hard

"@radix-ui/react-toggle@npm:1.1.9, @radix-ui/react-toggle@npm:^1.1.9":
  version: 1.1.9
  resolution: "@radix-ui/react-toggle@npm:1.1.9"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.1.3"
    "@radix-ui/react-use-controllable-state": "npm:1.2.2"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/af60c49bec8bb2b7984d95bb92458b83a2c55ce1e9933eaf3d90d04029a1117345666a624ea9866badaedb6d131c14d8042327ae66e186cbc06a334f2c07e1b7
  languageName: node
  linkType: hard

"@radix-ui/react-tooltip@npm:^1.2.7":
  version: 1.2.7
  resolution: "@radix-ui/react-tooltip@npm:1.2.7"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.2"
    "@radix-ui/react-compose-refs": "npm:1.1.2"
    "@radix-ui/react-context": "npm:1.1.2"
    "@radix-ui/react-dismissable-layer": "npm:1.1.10"
    "@radix-ui/react-id": "npm:1.1.1"
    "@radix-ui/react-popper": "npm:1.2.7"
    "@radix-ui/react-portal": "npm:1.1.9"
    "@radix-ui/react-presence": "npm:1.1.4"
    "@radix-ui/react-primitive": "npm:2.1.3"
    "@radix-ui/react-slot": "npm:1.2.3"
    "@radix-ui/react-use-controllable-state": "npm:1.2.2"
    "@radix-ui/react-visually-hidden": "npm:1.2.3"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/28798d576c6ffec4f11120cd563aa9d5ab9afb9a37dc18778176442756d026c8c46eec1ddc647b2b5914045495fcb89f82530106e91acb55776b7d6b1a10fb57
  languageName: node
  linkType: hard

"@radix-ui/react-use-callback-ref@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-callback-ref@npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/5f6aff8592dea6a7e46589808912aba3fb3b626cf6edd2b14f01638b61dbbe49eeb9f67cd5601f4c15b2fb547b9a7e825f7c4961acd4dd70176c969ae405f8d8
  languageName: node
  linkType: hard

"@radix-ui/react-use-controllable-state@npm:1.2.2":
  version: 1.2.2
  resolution: "@radix-ui/react-use-controllable-state@npm:1.2.2"
  dependencies:
    "@radix-ui/react-use-effect-event": "npm:0.0.2"
    "@radix-ui/react-use-layout-effect": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/f55c4b06e895293aed4b44c9ef26fb24432539f5346fcd6519c7745800535b571058685314e83486a45bf61dc83887e24826490d3068acc317fb0a9010516e63
  languageName: node
  linkType: hard

"@radix-ui/react-use-effect-event@npm:0.0.2":
  version: 0.0.2
  resolution: "@radix-ui/react-use-effect-event@npm:0.0.2"
  dependencies:
    "@radix-ui/react-use-layout-effect": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/e84ff72a3e76c5ae9c94941028bb4b6472f17d4104481b9eab773deab3da640ecea035e54da9d6f4df8d84c18ef6913baf92b7511bee06930dc58bd0c0add417
  languageName: node
  linkType: hard

"@radix-ui/react-use-escape-keydown@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-escape-keydown@npm:1.1.1"
  dependencies:
    "@radix-ui/react-use-callback-ref": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/bff53be99e940fef1d3c4df7d560e1d9133182e5a98336255d3063327d1d3dd4ec54a95dc5afe15cca4fb6c184f0a956c70de2815578c318cf995a7f9beabaa1
  languageName: node
  linkType: hard

"@radix-ui/react-use-is-hydrated@npm:0.1.0":
  version: 0.1.0
  resolution: "@radix-ui/react-use-is-hydrated@npm:0.1.0"
  dependencies:
    use-sync-external-store: "npm:^1.5.0"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/635079bafe32829fc7405895154568ea94a22689b170489fd6d77668e4885e72ff71ed6d0ea3d602852841ef0f1927aa400fee2178d5dfbeb8bc9297da7d6498
  languageName: node
  linkType: hard

"@radix-ui/react-use-layout-effect@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-layout-effect@npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/9f98fdaba008dfc58050de60a77670b885792df473cf82c1cef8daee919a5dd5a77d270209f5f0b0abfaac78cb1627396e3ff56c81b735be550409426fe8b040
  languageName: node
  linkType: hard

"@radix-ui/react-use-previous@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-previous@npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/52f1089d941491cd59b7f52a5679a14e9381711419a0557ce0f3bc9a4c117078224efec54dcced41a3653a13a386a7b6ec75435d61a273e8b9f5d00235f2b182
  languageName: node
  linkType: hard

"@radix-ui/react-use-rect@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-rect@npm:1.1.1"
  dependencies:
    "@radix-ui/rect": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/271711404c05c589c8dbdaa748749e7daf44bcc6bffc9ecd910821c3ebca0ee245616cf5b39653ce690f53f875c3836fd3f36f51ab1c628273b6db599eee4864
  languageName: node
  linkType: hard

"@radix-ui/react-use-size@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-size@npm:1.1.1"
  dependencies:
    "@radix-ui/react-use-layout-effect": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/851d09a816f44282e0e9e2147b1b571410174cc048703a50c4fa54d672de994fd1dfff1da9d480ecfd12c77ae8f48d74f01adaf668f074156b8cd0043c6c21d8
  languageName: node
  linkType: hard

"@radix-ui/react-visually-hidden@npm:1.2.3":
  version: 1.2.3
  resolution: "@radix-ui/react-visually-hidden@npm:1.2.3"
  dependencies:
    "@radix-ui/react-primitive": "npm:2.1.3"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10c0/cf86a37f1cbee50a964056f3dc4f6bb1ee79c76daa321f913aa20ff3e1ccdfafbf2b114d7bb616aeefc7c4b895e6ca898523fdb67710d89bd5d8edb739a0d9b6
  languageName: node
  linkType: hard

"@radix-ui/rect@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/rect@npm:1.1.1"
  checksum: 10c0/0dac4f0f15691199abe6a0e067821ddd9d0349c0c05f39834e4eafc8403caf724106884035ae91bbc826e10367e6a5672e7bec4d4243860fa7649de246b1f60b
  languageName: node
  linkType: hard

"@rolldown/pluginutils@npm:1.0.0-beta.9":
  version: 1.0.0-beta.9
  resolution: "@rolldown/pluginutils@npm:1.0.0-beta.9"
  checksum: 10c0/21aebb7ebd093282efd96f63ddd465f76746b1d70282366d6ccc7fff6eb4da5c2f8f4bfaaaeb4283c2432600e5609e39e9897864575e593efc11d376ca1a6fa1
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm-eabi@npm:4.42.0":
  version: 4.42.0
  resolution: "@rollup/rollup-android-arm-eabi@npm:4.42.0"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm64@npm:4.42.0":
  version: 4.42.0
  resolution: "@rollup/rollup-android-arm64@npm:4.42.0"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-arm64@npm:4.42.0":
  version: 4.42.0
  resolution: "@rollup/rollup-darwin-arm64@npm:4.42.0"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-x64@npm:4.42.0":
  version: 4.42.0
  resolution: "@rollup/rollup-darwin-x64@npm:4.42.0"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-arm64@npm:4.42.0":
  version: 4.42.0
  resolution: "@rollup/rollup-freebsd-arm64@npm:4.42.0"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-x64@npm:4.42.0":
  version: 4.42.0
  resolution: "@rollup/rollup-freebsd-x64@npm:4.42.0"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-gnueabihf@npm:4.42.0":
  version: 4.42.0
  resolution: "@rollup/rollup-linux-arm-gnueabihf@npm:4.42.0"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-musleabihf@npm:4.42.0":
  version: 4.42.0
  resolution: "@rollup/rollup-linux-arm-musleabihf@npm:4.42.0"
  conditions: os=linux & cpu=arm & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-gnu@npm:4.42.0":
  version: 4.42.0
  resolution: "@rollup/rollup-linux-arm64-gnu@npm:4.42.0"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-musl@npm:4.42.0":
  version: 4.42.0
  resolution: "@rollup/rollup-linux-arm64-musl@npm:4.42.0"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-loongarch64-gnu@npm:4.42.0":
  version: 4.42.0
  resolution: "@rollup/rollup-linux-loongarch64-gnu@npm:4.42.0"
  conditions: os=linux & cpu=loong64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-powerpc64le-gnu@npm:4.42.0":
  version: 4.42.0
  resolution: "@rollup/rollup-linux-powerpc64le-gnu@npm:4.42.0"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-gnu@npm:4.42.0":
  version: 4.42.0
  resolution: "@rollup/rollup-linux-riscv64-gnu@npm:4.42.0"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-musl@npm:4.42.0":
  version: 4.42.0
  resolution: "@rollup/rollup-linux-riscv64-musl@npm:4.42.0"
  conditions: os=linux & cpu=riscv64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-s390x-gnu@npm:4.42.0":
  version: 4.42.0
  resolution: "@rollup/rollup-linux-s390x-gnu@npm:4.42.0"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-gnu@npm:4.42.0":
  version: 4.42.0
  resolution: "@rollup/rollup-linux-x64-gnu@npm:4.42.0"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-musl@npm:4.42.0":
  version: 4.42.0
  resolution: "@rollup/rollup-linux-x64-musl@npm:4.42.0"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-win32-arm64-msvc@npm:4.42.0":
  version: 4.42.0
  resolution: "@rollup/rollup-win32-arm64-msvc@npm:4.42.0"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-win32-ia32-msvc@npm:4.42.0":
  version: 4.42.0
  resolution: "@rollup/rollup-win32-ia32-msvc@npm:4.42.0"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@rollup/rollup-win32-x64-msvc@npm:4.42.0":
  version: 4.42.0
  resolution: "@rollup/rollup-win32-x64-msvc@npm:4.42.0"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@tailwindcss/node@npm:4.1.8":
  version: 4.1.8
  resolution: "@tailwindcss/node@npm:4.1.8"
  dependencies:
    "@ampproject/remapping": "npm:^2.3.0"
    enhanced-resolve: "npm:^5.18.1"
    jiti: "npm:^2.4.2"
    lightningcss: "npm:1.30.1"
    magic-string: "npm:^0.30.17"
    source-map-js: "npm:^1.2.1"
    tailwindcss: "npm:4.1.8"
  checksum: 10c0/c6e3cfad831bce7f1ed6a218be11c8c722589499a85df698e815bb4274329cd0ef9e24846e6bf2fc72acd064b1e1ac92e8ec98a8f669ffc0935db2dc2a7f0436
  languageName: node
  linkType: hard

"@tailwindcss/oxide-android-arm64@npm:4.1.8":
  version: 4.1.8
  resolution: "@tailwindcss/oxide-android-arm64@npm:4.1.8"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@tailwindcss/oxide-darwin-arm64@npm:4.1.8":
  version: 4.1.8
  resolution: "@tailwindcss/oxide-darwin-arm64@npm:4.1.8"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@tailwindcss/oxide-darwin-x64@npm:4.1.8":
  version: 4.1.8
  resolution: "@tailwindcss/oxide-darwin-x64@npm:4.1.8"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@tailwindcss/oxide-freebsd-x64@npm:4.1.8":
  version: 4.1.8
  resolution: "@tailwindcss/oxide-freebsd-x64@npm:4.1.8"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@tailwindcss/oxide-linux-arm-gnueabihf@npm:4.1.8":
  version: 4.1.8
  resolution: "@tailwindcss/oxide-linux-arm-gnueabihf@npm:4.1.8"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@tailwindcss/oxide-linux-arm64-gnu@npm:4.1.8":
  version: 4.1.8
  resolution: "@tailwindcss/oxide-linux-arm64-gnu@npm:4.1.8"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@tailwindcss/oxide-linux-arm64-musl@npm:4.1.8":
  version: 4.1.8
  resolution: "@tailwindcss/oxide-linux-arm64-musl@npm:4.1.8"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@tailwindcss/oxide-linux-x64-gnu@npm:4.1.8":
  version: 4.1.8
  resolution: "@tailwindcss/oxide-linux-x64-gnu@npm:4.1.8"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@tailwindcss/oxide-linux-x64-musl@npm:4.1.8":
  version: 4.1.8
  resolution: "@tailwindcss/oxide-linux-x64-musl@npm:4.1.8"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@tailwindcss/oxide-wasm32-wasi@npm:4.1.8":
  version: 4.1.8
  resolution: "@tailwindcss/oxide-wasm32-wasi@npm:4.1.8"
  dependencies:
    "@emnapi/core": "npm:^1.4.3"
    "@emnapi/runtime": "npm:^1.4.3"
    "@emnapi/wasi-threads": "npm:^1.0.2"
    "@napi-rs/wasm-runtime": "npm:^0.2.10"
    "@tybys/wasm-util": "npm:^0.9.0"
    tslib: "npm:^2.8.0"
  conditions: cpu=wasm32
  languageName: node
  linkType: hard

"@tailwindcss/oxide-win32-arm64-msvc@npm:4.1.8":
  version: 4.1.8
  resolution: "@tailwindcss/oxide-win32-arm64-msvc@npm:4.1.8"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@tailwindcss/oxide-win32-x64-msvc@npm:4.1.8":
  version: 4.1.8
  resolution: "@tailwindcss/oxide-win32-x64-msvc@npm:4.1.8"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@tailwindcss/oxide@npm:4.1.8":
  version: 4.1.8
  resolution: "@tailwindcss/oxide@npm:4.1.8"
  dependencies:
    "@tailwindcss/oxide-android-arm64": "npm:4.1.8"
    "@tailwindcss/oxide-darwin-arm64": "npm:4.1.8"
    "@tailwindcss/oxide-darwin-x64": "npm:4.1.8"
    "@tailwindcss/oxide-freebsd-x64": "npm:4.1.8"
    "@tailwindcss/oxide-linux-arm-gnueabihf": "npm:4.1.8"
    "@tailwindcss/oxide-linux-arm64-gnu": "npm:4.1.8"
    "@tailwindcss/oxide-linux-arm64-musl": "npm:4.1.8"
    "@tailwindcss/oxide-linux-x64-gnu": "npm:4.1.8"
    "@tailwindcss/oxide-linux-x64-musl": "npm:4.1.8"
    "@tailwindcss/oxide-wasm32-wasi": "npm:4.1.8"
    "@tailwindcss/oxide-win32-arm64-msvc": "npm:4.1.8"
    "@tailwindcss/oxide-win32-x64-msvc": "npm:4.1.8"
    detect-libc: "npm:^2.0.4"
    tar: "npm:^7.4.3"
  dependenciesMeta:
    "@tailwindcss/oxide-android-arm64":
      optional: true
    "@tailwindcss/oxide-darwin-arm64":
      optional: true
    "@tailwindcss/oxide-darwin-x64":
      optional: true
    "@tailwindcss/oxide-freebsd-x64":
      optional: true
    "@tailwindcss/oxide-linux-arm-gnueabihf":
      optional: true
    "@tailwindcss/oxide-linux-arm64-gnu":
      optional: true
    "@tailwindcss/oxide-linux-arm64-musl":
      optional: true
    "@tailwindcss/oxide-linux-x64-gnu":
      optional: true
    "@tailwindcss/oxide-linux-x64-musl":
      optional: true
    "@tailwindcss/oxide-wasm32-wasi":
      optional: true
    "@tailwindcss/oxide-win32-arm64-msvc":
      optional: true
    "@tailwindcss/oxide-win32-x64-msvc":
      optional: true
  checksum: 10c0/806246b8a82d079ab50628a7a9d69f23a6b09280e55859f484d3a5250108e58c6188230aa203bfb352b6a6bbdb68a6871c37c9b1f94fe67a26eafe853973f08d
  languageName: node
  linkType: hard

"@tailwindcss/vite@npm:^4.0.15":
  version: 4.1.8
  resolution: "@tailwindcss/vite@npm:4.1.8"
  dependencies:
    "@tailwindcss/node": "npm:4.1.8"
    "@tailwindcss/oxide": "npm:4.1.8"
    tailwindcss: "npm:4.1.8"
  peerDependencies:
    vite: ^5.2.0 || ^6
  checksum: 10c0/334384b196aebb91d9e2dae0a3959f404fdb570bb590c69786b0bdd27d7bfded37c0ad439fccb8c16f3180232820eeeb1dd05d97006e22dabdc2f9a35111e84b
  languageName: node
  linkType: hard

"@tybys/wasm-util@npm:^0.9.0":
  version: 0.9.0
  resolution: "@tybys/wasm-util@npm:0.9.0"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10c0/f9fde5c554455019f33af6c8215f1a1435028803dc2a2825b077d812bed4209a1a64444a4ca0ce2ea7e1175c8d88e2f9173a36a33c199e8a5c671aa31de8242d
  languageName: node
  linkType: hard

"@types/babel__core@npm:^7.20.5":
  version: 7.20.5
  resolution: "@types/babel__core@npm:7.20.5"
  dependencies:
    "@babel/parser": "npm:^7.20.7"
    "@babel/types": "npm:^7.20.7"
    "@types/babel__generator": "npm:*"
    "@types/babel__template": "npm:*"
    "@types/babel__traverse": "npm:*"
  checksum: 10c0/bdee3bb69951e833a4b811b8ee9356b69a61ed5b7a23e1a081ec9249769117fa83aaaf023bb06562a038eb5845155ff663e2d5c75dd95c1d5ccc91db012868ff
  languageName: node
  linkType: hard

"@types/babel__generator@npm:*":
  version: 7.27.0
  resolution: "@types/babel__generator@npm:7.27.0"
  dependencies:
    "@babel/types": "npm:^7.0.0"
  checksum: 10c0/9f9e959a8792df208a9d048092fda7e1858bddc95c6314857a8211a99e20e6830bdeb572e3587ae8be5429e37f2a96fcf222a9f53ad232f5537764c9e13a2bbd
  languageName: node
  linkType: hard

"@types/babel__template@npm:*":
  version: 7.4.4
  resolution: "@types/babel__template@npm:7.4.4"
  dependencies:
    "@babel/parser": "npm:^7.1.0"
    "@babel/types": "npm:^7.0.0"
  checksum: 10c0/cc84f6c6ab1eab1427e90dd2b76ccee65ce940b778a9a67be2c8c39e1994e6f5bbc8efa309f6cea8dc6754994524cd4d2896558df76d92e7a1f46ecffee7112b
  languageName: node
  linkType: hard

"@types/babel__traverse@npm:*":
  version: 7.20.7
  resolution: "@types/babel__traverse@npm:7.20.7"
  dependencies:
    "@babel/types": "npm:^7.20.7"
  checksum: 10c0/5386f0af44f8746b063b87418f06129a814e16bb2686965a575e9d7376b360b088b89177778d8c426012abc43dd1a2d8ec3218bfc382280c898682746ce2ffbd
  languageName: node
  linkType: hard

"@types/body-parser@npm:*":
  version: 1.19.6
  resolution: "@types/body-parser@npm:1.19.6"
  dependencies:
    "@types/connect": "npm:*"
    "@types/node": "npm:*"
  checksum: 10c0/542da05c924dce58ee23f50a8b981fee36921850c82222e384931fda3e106f750f7880c47be665217d72dbe445129049db6eb1f44e7a06b09d62af8f3cca8ea7
  languageName: node
  linkType: hard

"@types/connect@npm:*":
  version: 3.4.38
  resolution: "@types/connect@npm:3.4.38"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/2e1cdba2c410f25649e77856505cd60223250fa12dff7a503e492208dbfdd25f62859918f28aba95315251fd1f5e1ffbfca1e25e73037189ab85dd3f8d0a148c
  languageName: node
  linkType: hard

"@types/debug@npm:^4.0.0":
  version: 4.1.12
  resolution: "@types/debug@npm:4.1.12"
  dependencies:
    "@types/ms": "npm:*"
  checksum: 10c0/5dcd465edbb5a7f226e9a5efd1f399c6172407ef5840686b73e3608ce135eeca54ae8037dcd9f16bdb2768ac74925b820a8b9ecc588a58ca09eca6acabe33e2f
  languageName: node
  linkType: hard

"@types/dom-speech-recognition@npm:*":
  version: 0.0.6
  resolution: "@types/dom-speech-recognition@npm:0.0.6"
  checksum: 10c0/2483e59689b62c5be1d5da5168ee49be933d91e6866d5f60978b31aaf1f254d921c67f4c795c14a90442b0f86d51496cec88d26c810cf6e3124db1880f798c13
  languageName: node
  linkType: hard

"@types/estree-jsx@npm:^1.0.0":
  version: 1.0.5
  resolution: "@types/estree-jsx@npm:1.0.5"
  dependencies:
    "@types/estree": "npm:*"
  checksum: 10c0/07b354331516428b27a3ab99ee397547d47eb223c34053b48f84872fafb841770834b90cc1a0068398e7c7ccb15ec51ab00ec64b31dc5e3dbefd624638a35c6d
  languageName: node
  linkType: hard

"@types/estree@npm:*, @types/estree@npm:^1.0.0, @types/estree@npm:^1.0.6":
  version: 1.0.8
  resolution: "@types/estree@npm:1.0.8"
  checksum: 10c0/39d34d1afaa338ab9763f37ad6066e3f349444f9052b9676a7cc0252ef9485a41c6d81c9c4e0d26e9077993354edf25efc853f3224dd4b447175ef62bdcc86a5
  languageName: node
  linkType: hard

"@types/estree@npm:1.0.7":
  version: 1.0.7
  resolution: "@types/estree@npm:1.0.7"
  checksum: 10c0/be815254316882f7c40847336cd484c3bc1c3e34f710d197160d455dc9d6d050ffbf4c3bc76585dba86f737f020ab20bdb137ebe0e9116b0c86c7c0342221b8c
  languageName: node
  linkType: hard

"@types/express-serve-static-core@npm:^5.0.0":
  version: 5.0.6
  resolution: "@types/express-serve-static-core@npm:5.0.6"
  dependencies:
    "@types/node": "npm:*"
    "@types/qs": "npm:*"
    "@types/range-parser": "npm:*"
    "@types/send": "npm:*"
  checksum: 10c0/aced8cc88c1718adbbd1fc488756b0f22d763368d9eff2ae21b350698fab4a77d8d13c3699056dc662a887e43a8b67a3e8f6289ff76102ecc6bad4a7710d31a6
  languageName: node
  linkType: hard

"@types/express@npm:^5.0.1":
  version: 5.0.3
  resolution: "@types/express@npm:5.0.3"
  dependencies:
    "@types/body-parser": "npm:*"
    "@types/express-serve-static-core": "npm:^5.0.0"
    "@types/serve-static": "npm:*"
  checksum: 10c0/f0fbc8daa7f40070b103cf4d020ff1dd08503477d866d1134b87c0390bba71d5d7949cb8b4e719a81ccba89294d8e1573414e6dcbb5bb1d097a7b820928ebdef
  languageName: node
  linkType: hard

"@types/hast@npm:^3.0.0":
  version: 3.0.4
  resolution: "@types/hast@npm:3.0.4"
  dependencies:
    "@types/unist": "npm:*"
  checksum: 10c0/3249781a511b38f1d330fd1e3344eed3c4e7ea8eff82e835d35da78e637480d36fad37a78be5a7aed8465d237ad0446abc1150859d0fde395354ea634decf9f7
  languageName: node
  linkType: hard

"@types/http-errors@npm:*":
  version: 2.0.5
  resolution: "@types/http-errors@npm:2.0.5"
  checksum: 10c0/00f8140fbc504f47356512bd88e1910c2f07e04233d99c88c854b3600ce0523c8cd0ba7d1897667243282eb44c59abb9245959e2428b9de004f93937f52f7c15
  languageName: node
  linkType: hard

"@types/js-cookie@npm:^2.2.6":
  version: 2.2.7
  resolution: "@types/js-cookie@npm:2.2.7"
  checksum: 10c0/29196c6829982b5efa79117122a7d62cf4bc2f6397ce8eac1539319ff5dce3b44b2d86f2ac064f2ed3488fb24439358f24af6914fde5c5c4bab9a85728a13a6f
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.15":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 10c0/a996a745e6c5d60292f36731dd41341339d4eeed8180bb09226e5c8d23759067692b1d88e5d91d72ee83dfc00d3aca8e7bd43ea120516c17922cbcb7c3e252db
  languageName: node
  linkType: hard

"@types/long@npm:^4.0.1":
  version: 4.0.2
  resolution: "@types/long@npm:4.0.2"
  checksum: 10c0/42ec66ade1f72ff9d143c5a519a65efc7c1c77be7b1ac5455c530ae9acd87baba065542f8847522af2e3ace2cc999f3ad464ef86e6b7352eece34daf88f8c924
  languageName: node
  linkType: hard

"@types/mdast@npm:^4.0.0":
  version: 4.0.4
  resolution: "@types/mdast@npm:4.0.4"
  dependencies:
    "@types/unist": "npm:*"
  checksum: 10c0/84f403dbe582ee508fd9c7643ac781ad8597fcbfc9ccb8d4715a2c92e4545e5772cbd0dbdf18eda65789386d81b009967fdef01b24faf6640f817287f54d9c82
  languageName: node
  linkType: hard

"@types/mime@npm:^1":
  version: 1.3.5
  resolution: "@types/mime@npm:1.3.5"
  checksum: 10c0/c2ee31cd9b993804df33a694d5aa3fa536511a49f2e06eeab0b484fef59b4483777dbb9e42a4198a0809ffbf698081fdbca1e5c2218b82b91603dfab10a10fbc
  languageName: node
  linkType: hard

"@types/ms@npm:*":
  version: 2.1.0
  resolution: "@types/ms@npm:2.1.0"
  checksum: 10c0/5ce692ffe1549e1b827d99ef8ff71187457e0eb44adbae38fdf7b9a74bae8d20642ee963c14516db1d35fa2652e65f47680fdf679dcbde52bbfadd021f497225
  languageName: node
  linkType: hard

"@types/node-fetch@npm:^2.6.4":
  version: 2.6.12
  resolution: "@types/node-fetch@npm:2.6.12"
  dependencies:
    "@types/node": "npm:*"
    form-data: "npm:^4.0.0"
  checksum: 10c0/7693acad5499b7df2d1727d46cff092a63896dc04645f36b973dd6dd754a59a7faba76fcb777bdaa35d80625c6a9dd7257cca9c401a4bab03b04480cda7fd1af
  languageName: node
  linkType: hard

"@types/node@npm:*, @types/node@npm:>=13.7.0, @types/node@npm:^22.15.21":
  version: 22.15.30
  resolution: "@types/node@npm:22.15.30"
  dependencies:
    undici-types: "npm:~6.21.0"
  checksum: 10c0/ca330ac0e7fd502686d6df115fcc606aba46fd334220f749bbba2f639accdadcb23f7900603ceccdc8240be736739cad5c0b87c0fa92c9255a4dff245f07d664
  languageName: node
  linkType: hard

"@types/node@npm:^18.11.18":
  version: 18.19.111
  resolution: "@types/node@npm:18.19.111"
  dependencies:
    undici-types: "npm:~5.26.4"
  checksum: 10c0/21e4a5382ce84f4ac62e3fe21b63b699c9e1ff92114d931634959fe0f02f63317987ba05726c731486b2d11a6356e74a4f1fb05a23019448f91401d592fc1ec8
  languageName: node
  linkType: hard

"@types/qs@npm:*":
  version: 6.14.0
  resolution: "@types/qs@npm:6.14.0"
  checksum: 10c0/5b3036df6e507483869cdb3858201b2e0b64b4793dc4974f188caa5b5732f2333ab9db45c08157975054d3b070788b35088b4bc60257ae263885016ee2131310
  languageName: node
  linkType: hard

"@types/range-parser@npm:*":
  version: 1.2.7
  resolution: "@types/range-parser@npm:1.2.7"
  checksum: 10c0/361bb3e964ec5133fa40644a0b942279ed5df1949f21321d77de79f48b728d39253e5ce0408c9c17e4e0fd95ca7899da36841686393b9f7a1e209916e9381a3c
  languageName: node
  linkType: hard

"@types/react-dom@npm:^19.0.4":
  version: 19.1.6
  resolution: "@types/react-dom@npm:19.1.6"
  peerDependencies:
    "@types/react": ^19.0.0
  checksum: 10c0/7ba74eee2919e3f225e898b65fdaa16e54952aaf9e3472a080ddc82ca54585e46e60b3c52018d21d4b7053f09d27b8293e9f468b85f9932ff452cd290cc131e8
  languageName: node
  linkType: hard

"@types/react-speech-recognition@npm:^3.9.6":
  version: 3.9.6
  resolution: "@types/react-speech-recognition@npm:3.9.6"
  dependencies:
    "@types/dom-speech-recognition": "npm:*"
  checksum: 10c0/d39d3fc44f2c5314f6d24864f7d8197271f6529640a46e33965c32ffd4aaba66359d4205a52960a675a07078c6b08ff479fc71ea208c22ec3262230b5fcc4dd6
  languageName: node
  linkType: hard

"@types/react@npm:^19.0.10":
  version: 19.1.6
  resolution: "@types/react@npm:19.1.6"
  dependencies:
    csstype: "npm:^3.0.2"
  checksum: 10c0/8b10b198e28997b3c57559750f8bcf5ae7b33c554b16b6f4fe2ece1d4de6a2fc8cb53e7effe08ec9cb939d2f479eb97c5e08aac2cf83b10a90164fe451cc8ea2
  languageName: node
  linkType: hard

"@types/retry@npm:0.12.0":
  version: 0.12.0
  resolution: "@types/retry@npm:0.12.0"
  checksum: 10c0/7c5c9086369826f569b83a4683661557cab1361bac0897a1cefa1a915ff739acd10ca0d62b01071046fe3f5a3f7f2aec80785fe283b75602dc6726781ea3e328
  languageName: node
  linkType: hard

"@types/send@npm:*":
  version: 0.17.5
  resolution: "@types/send@npm:0.17.5"
  dependencies:
    "@types/mime": "npm:^1"
    "@types/node": "npm:*"
  checksum: 10c0/a86c9b89bb0976ff58c1cdd56360ea98528f4dbb18a5c2287bb8af04815513a576a42b4e0e1e7c4d14f7d6ea54733f6ef935ebff8c65e86d9c222881a71e1f15
  languageName: node
  linkType: hard

"@types/serve-static@npm:*":
  version: 1.15.8
  resolution: "@types/serve-static@npm:1.15.8"
  dependencies:
    "@types/http-errors": "npm:*"
    "@types/node": "npm:*"
    "@types/send": "npm:*"
  checksum: 10c0/8ad86a25b87da5276cb1008c43c74667ff7583904d46d5fcaf0355887869d859d453d7dc4f890788ae04705c23720e9b6b6f3215e2d1d2a4278bbd090a9268dd
  languageName: node
  linkType: hard

"@types/unist@npm:*, @types/unist@npm:^3.0.0":
  version: 3.0.3
  resolution: "@types/unist@npm:3.0.3"
  checksum: 10c0/2b1e4adcab78388e088fcc3c0ae8700f76619dbcb4741d7d201f87e2cb346bfc29a89003cfea2d76c996e1061452e14fcd737e8b25aacf949c1f2d6b2bc3dd60
  languageName: node
  linkType: hard

"@types/unist@npm:^2.0.0":
  version: 2.0.11
  resolution: "@types/unist@npm:2.0.11"
  checksum: 10c0/24dcdf25a168f453bb70298145eb043cfdbb82472db0bc0b56d6d51cd2e484b9ed8271d4ac93000a80da568f2402e9339723db262d0869e2bf13bc58e081768d
  languageName: node
  linkType: hard

"@types/uuid@npm:^10.0.0":
  version: 10.0.0
  resolution: "@types/uuid@npm:10.0.0"
  checksum: 10c0/9a1404bf287164481cb9b97f6bb638f78f955be57c40c6513b7655160beb29df6f84c915aaf4089a1559c216557dc4d2f79b48d978742d3ae10b937420ddac60
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:8.33.1":
  version: 8.33.1
  resolution: "@typescript-eslint/eslint-plugin@npm:8.33.1"
  dependencies:
    "@eslint-community/regexpp": "npm:^4.10.0"
    "@typescript-eslint/scope-manager": "npm:8.33.1"
    "@typescript-eslint/type-utils": "npm:8.33.1"
    "@typescript-eslint/utils": "npm:8.33.1"
    "@typescript-eslint/visitor-keys": "npm:8.33.1"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^7.0.0"
    natural-compare: "npm:^1.4.0"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    "@typescript-eslint/parser": ^8.33.1
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/35544068f175ca25296b42d0905065b40653a92c62e55414be68f62ddab580d7d768ee3c1276195fd8b8dd49de738ab7b41b8685e6fe2cd341cfca7320569166
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:8.33.1":
  version: 8.33.1
  resolution: "@typescript-eslint/parser@npm:8.33.1"
  dependencies:
    "@typescript-eslint/scope-manager": "npm:8.33.1"
    "@typescript-eslint/types": "npm:8.33.1"
    "@typescript-eslint/typescript-estree": "npm:8.33.1"
    "@typescript-eslint/visitor-keys": "npm:8.33.1"
    debug: "npm:^4.3.4"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/be1c1313c342d956f5adfbd56f79865894cc9cabf93992515a690559c3758538868270671b222f90e4cabc2dcab82256aeb3ccea7502de9cc69e47b9b17ed45f
  languageName: node
  linkType: hard

"@typescript-eslint/project-service@npm:8.33.1":
  version: 8.33.1
  resolution: "@typescript-eslint/project-service@npm:8.33.1"
  dependencies:
    "@typescript-eslint/tsconfig-utils": "npm:^8.33.1"
    "@typescript-eslint/types": "npm:^8.33.1"
    debug: "npm:^4.3.4"
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/b2ff7653aef4648bdff8aafc69b9de434184827216709f8a36427536ac7082a8adf1c5ac12a0a2bb023b46dfad8f6fee238028acc94af622956af7f22362de6f
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:8.33.1":
  version: 8.33.1
  resolution: "@typescript-eslint/scope-manager@npm:8.33.1"
  dependencies:
    "@typescript-eslint/types": "npm:8.33.1"
    "@typescript-eslint/visitor-keys": "npm:8.33.1"
  checksum: 10c0/03a6fd2b0a8ebeb62083a8f51658f0c42391cbfb632411542569a3a227d53bdb0332026ef4d5adc4780e5350d1d8b89e5b19667ed899afd26506e60c70192692
  languageName: node
  linkType: hard

"@typescript-eslint/tsconfig-utils@npm:8.33.1, @typescript-eslint/tsconfig-utils@npm:^8.33.1":
  version: 8.33.1
  resolution: "@typescript-eslint/tsconfig-utils@npm:8.33.1"
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/242e8f271d2e6e51446d337e1e59e8c91b66c0241da0fb861f536eb86cc3b53d1727c41e12e1ba070fa2451c8bc517c1ec50decaffa92a7c612b2aba29872777
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:8.33.1":
  version: 8.33.1
  resolution: "@typescript-eslint/type-utils@npm:8.33.1"
  dependencies:
    "@typescript-eslint/typescript-estree": "npm:8.33.1"
    "@typescript-eslint/utils": "npm:8.33.1"
    debug: "npm:^4.3.4"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/59843eeb7c652306d130104d7cb0f7dea1cc95a6cf6345609efbae130f24e3c4a9472780332af4247337e152b7955540b15fd9b907c04a5d265b888139818266
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:8.33.1, @typescript-eslint/types@npm:^8.33.1":
  version: 8.33.1
  resolution: "@typescript-eslint/types@npm:8.33.1"
  checksum: 10c0/3083c184c882475eed1f9d1a8961dad30ef834c662bc826ff9a959ff1eed49aad21a73b2b93c4062799feafff5f5f24aebb1df17e198808aa19d4c8de1e64095
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:8.33.1":
  version: 8.33.1
  resolution: "@typescript-eslint/typescript-estree@npm:8.33.1"
  dependencies:
    "@typescript-eslint/project-service": "npm:8.33.1"
    "@typescript-eslint/tsconfig-utils": "npm:8.33.1"
    "@typescript-eslint/types": "npm:8.33.1"
    "@typescript-eslint/visitor-keys": "npm:8.33.1"
    debug: "npm:^4.3.4"
    fast-glob: "npm:^3.3.2"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:^9.0.4"
    semver: "npm:^7.6.0"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/293a93d25046e05fdc3887232191c3f3ee771c0f5b1426d63deaf0541db1cb80b4307a80805c78b092206c9b267884a7e6b5905dc1b3c26f28bb4de47fd9ee8f
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:8.33.1":
  version: 8.33.1
  resolution: "@typescript-eslint/utils@npm:8.33.1"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.7.0"
    "@typescript-eslint/scope-manager": "npm:8.33.1"
    "@typescript-eslint/types": "npm:8.33.1"
    "@typescript-eslint/typescript-estree": "npm:8.33.1"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/12263df6eb32e8175236ad899687c062b50cfe4a0e66307d25ad2bf85a3e911faacbfbea4df180a59ebb5913fe1cc1f53fe3914695c7d802dd318bbc846fea26
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:8.33.1":
  version: 8.33.1
  resolution: "@typescript-eslint/visitor-keys@npm:8.33.1"
  dependencies:
    "@typescript-eslint/types": "npm:8.33.1"
    eslint-visitor-keys: "npm:^4.2.0"
  checksum: 10c0/3eb99072e7c2741d5dfc38945d1e7617b15ed10d06b24658a6e919e4153983b3d3c5f5f775ce140f83a84dbde219948d187de97defb09c1a91f3cf0a96704a94
  languageName: node
  linkType: hard

"@ungap/structured-clone@npm:^1.0.0":
  version: 1.3.0
  resolution: "@ungap/structured-clone@npm:1.3.0"
  checksum: 10c0/0fc3097c2540ada1fc340ee56d58d96b5b536a2a0dab6e3ec17d4bfc8c4c86db345f61a375a8185f9da96f01c69678f836a2b57eeaa9e4b8eeafd26428e57b0a
  languageName: node
  linkType: hard

"@vitejs/plugin-react@npm:^4.3.4":
  version: 4.5.1
  resolution: "@vitejs/plugin-react@npm:4.5.1"
  dependencies:
    "@babel/core": "npm:^7.26.10"
    "@babel/plugin-transform-react-jsx-self": "npm:^7.25.9"
    "@babel/plugin-transform-react-jsx-source": "npm:^7.25.9"
    "@rolldown/pluginutils": "npm:1.0.0-beta.9"
    "@types/babel__core": "npm:^7.20.5"
    react-refresh: "npm:^0.17.0"
  peerDependencies:
    vite: ^4.2.0 || ^5.0.0 || ^6.0.0
  checksum: 10c0/51b28c02905228cc5b1dd16f5d008cf08175e47d22ff37b0959a9ee20a46a5215698b20405f39e6b73ae2a63c2a005d74c94b3456083ad038b59932d682b9165
  languageName: node
  linkType: hard

"@xenova/transformers@npm:^2.17.2":
  version: 2.17.2
  resolution: "@xenova/transformers@npm:2.17.2"
  dependencies:
    "@huggingface/jinja": "npm:^0.2.2"
    onnxruntime-node: "npm:1.14.0"
    onnxruntime-web: "npm:1.14.0"
    sharp: "npm:^0.32.0"
  dependenciesMeta:
    onnxruntime-node:
      optional: true
  checksum: 10c0/4111856accd1e5ac6ff3a3571c99c6f82230dd268ef0ba82e6efad8a8531626a7b7b5440901af90c0baaaca38f47f0a8b528e563955e5c4301474227f844a2ee
  languageName: node
  linkType: hard

"@xobotyi/scrollbar-width@npm:^1.9.5":
  version: 1.9.5
  resolution: "@xobotyi/scrollbar-width@npm:1.9.5"
  checksum: 10c0/4ebc79e4f798e2a5e89a5122f8fc4a086f08a92a44ac020599c4fe20d105b7d76ba06c094260b5f386a75e7ce6f6c518d9fc295228b651296b99c4477f986ac4
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: 10c0/21ba8f574ea57a3106d6d35623f2c4a9111d9ee3e9a5be47baed46ec2457d2eac46e07a5c4a60186f88cb98abbe3e24f2d4cca70bc2b12f1692523e2209a9ccf
  languageName: node
  linkType: hard

"abort-controller@npm:^3.0.0":
  version: 3.0.0
  resolution: "abort-controller@npm:3.0.0"
  dependencies:
    event-target-shim: "npm:^5.0.0"
  checksum: 10c0/90ccc50f010250152509a344eb2e71977fbf8db0ab8f1061197e3275ddf6c61a41a6edfd7b9409c664513131dd96e962065415325ef23efa5db931b382d24ca5
  languageName: node
  linkType: hard

"accepts@npm:~1.3.8":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: "npm:~2.1.34"
    negotiator: "npm:0.6.3"
  checksum: 10c0/3a35c5f5586cfb9a21163ca47a5f77ac34fa8ceb5d17d2fa2c0d81f41cbd7f8c6fa52c77e2c039acc0f4d09e71abdc51144246900f6bef5e3c4b333f77d89362
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10c0/4c54868fbef3b8d58927d5e33f0a4de35f59012fe7b12cf9dfbb345fb8f46607709e1c4431be869a23fb63c151033d84c4198fa9f79385cec34fcb1dd53974c1
  languageName: node
  linkType: hard

"acorn@npm:^8.14.0":
  version: 8.14.1
  resolution: "acorn@npm:8.14.1"
  bin:
    acorn: bin/acorn
  checksum: 10c0/dbd36c1ed1d2fa3550140000371fcf721578095b18777b85a79df231ca093b08edc6858d75d6e48c73e431c174dcf9214edbd7e6fa5911b93bd8abfa54e47123
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 10c0/6192b580c5b1d8fb399b9c62bf8343d76654c2dd62afcb9a52b2cf44a8b6ace1e3b704d3fe3547d91555c857d3df02603341ff2cb961b9cfe2b12f9f3c38ee11
  languageName: node
  linkType: hard

"agentkeepalive@npm:^4.2.1":
  version: 4.6.0
  resolution: "agentkeepalive@npm:4.6.0"
  dependencies:
    humanize-ms: "npm:^1.2.1"
  checksum: 10c0/235c182432f75046835b05f239708107138a40103deee23b6a08caee5136873709155753b394ec212e49e60e94a378189562cb01347765515cff61b692c69187
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 10c0/41e23642cbe545889245b9d2a45854ebba51cda6c778ebced9649420d9205f2efb39cb43dbc41e358409223b1ea43303ae4839db682c848b891e4811da1a5a71
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10c0/9a64bb8627b434ba9327b60c027742e5d17ac69277960d041898596271d992d4d52ba7267a63ca10232e29f6107fc8a835f6ce8d719b88c5f8493f8254813737
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10c0/a91daeddd54746338478eef88af3439a7edf30f8e23196e2d6ed182da9add559c601266dbef01c2efa46a958ad6f1f8b176799657616c702b5b02e799e7fd8dc
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10c0/895a23929da416f2bd3de7e9cb4eabd340949328ab85ddd6e484a637d8f6820d485f53933446f5291c3b760cbc488beb8e88573dd0f9c7daf83dccc8fe81b041
  languageName: node
  linkType: hard

"ansi-styles@npm:^5.0.0":
  version: 5.2.0
  resolution: "ansi-styles@npm:5.2.0"
  checksum: 10c0/9c4ca80eb3c2fb7b33841c210d2f20807f40865d27008d7c3f707b7f95cab7d67462a565e2388ac3285b71cb3d9bb2173de8da37c57692a362885ec34d6e27df
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10c0/5d1ec38c123984bcedd996eac680d548f31828bd679a66db2bdf11844634dde55fec3efa9c6bb1d89056a5e79c1ac540c4c784d592ea1d25028a92227d2f2d5c
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10c0/c5640c2d89045371c7cedd6a70212a04e360fd34d6edeae32f6952c63949e3525ea77dbec0289d8213a99bbaeab5abfa860b5c12cf88a2e6cf8106e90dd27a7e
  languageName: node
  linkType: hard

"aria-hidden@npm:^1.2.4":
  version: 1.2.6
  resolution: "aria-hidden@npm:1.2.6"
  dependencies:
    tslib: "npm:^2.0.0"
  checksum: 10c0/7720cb539497a9f760f68f98a4b30f22c6767aa0e72fa7d58279f7c164e258fc38b2699828f8de881aab0fc8e9c56d1313a3f1a965046fc0381a554dbc72b54a
  languageName: node
  linkType: hard

"array-flatten@npm:1.1.1":
  version: 1.1.1
  resolution: "array-flatten@npm:1.1.1"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10c0/d73e2ddf20c4eb9337e1b3df1a0f6159481050a5de457c55b14ea2e5cb6d90bb69e004c9af54737a5ee0917fcf2c9e25de67777bbe58261847846066ba75bc9d
  languageName: node
  linkType: hard

"b4a@npm:^1.6.4":
  version: 1.6.7
  resolution: "b4a@npm:1.6.7"
  checksum: 10c0/ec2f004d1daae04be8c5a1f8aeb7fea213c34025e279db4958eb0b82c1729ee25f7c6e89f92a5f65c8a9cf2d017ce27e3dda912403341d1781bd74528a4849d4
  languageName: node
  linkType: hard

"bail@npm:^2.0.0":
  version: 2.0.2
  resolution: "bail@npm:2.0.2"
  checksum: 10c0/25cbea309ef6a1f56214187004e8f34014eb015713ea01fa5b9b7e9e776ca88d0fdffd64143ac42dc91966c915a4b7b683411b56e14929fad16153fc026ffb8b
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10c0/9308baf0a7e4838a82bbfd11e01b1cb0f0cf2893bc1676c27c2a8c0e70cbae1c59120c3268517a8ae7fb6376b4639ef81ca22582611dbee4ed28df945134aaee
  languageName: node
  linkType: hard

"bare-events@npm:^2.2.0, bare-events@npm:^2.5.4":
  version: 2.5.4
  resolution: "bare-events@npm:2.5.4"
  checksum: 10c0/877a9cea73d545e2588cdbd6fd01653e27dac48ad6b44985cdbae73e1f57f292d4ba52e25d1fba53674c1053c463d159f3d5c7bc36a2e6e192e389b499ddd627
  languageName: node
  linkType: hard

"bare-fs@npm:^4.0.1":
  version: 4.1.5
  resolution: "bare-fs@npm:4.1.5"
  dependencies:
    bare-events: "npm:^2.5.4"
    bare-path: "npm:^3.0.0"
    bare-stream: "npm:^2.6.4"
  peerDependencies:
    bare-buffer: "*"
  peerDependenciesMeta:
    bare-buffer:
      optional: true
  checksum: 10c0/af72ec30bb7844524faa14ae2b74d13b08920b1d839c638da4ad1abdda643958d0b86653d284878a2f9160072b603c9dce55c8cc29da8d84e14ffce1c5d42a01
  languageName: node
  linkType: hard

"bare-os@npm:^3.0.1":
  version: 3.6.1
  resolution: "bare-os@npm:3.6.1"
  checksum: 10c0/13064789b3d0d3051d6a89424e6d861c08be101798d69faa78821cffb428b36d1fd4e17c824d5a4939bcd96dbff42c11921494139c8e53c3e520bc0e3f83aeee
  languageName: node
  linkType: hard

"bare-path@npm:^3.0.0":
  version: 3.0.0
  resolution: "bare-path@npm:3.0.0"
  dependencies:
    bare-os: "npm:^3.0.1"
  checksum: 10c0/56a3ca82a9f808f4976cb1188640ac206546ce0ddff582afafc7bd2a6a5b31c3bd16422653aec656eeada2830cfbaa433c6cbf6d6b4d9eba033d5e06d60d9a68
  languageName: node
  linkType: hard

"bare-stream@npm:^2.6.4":
  version: 2.6.5
  resolution: "bare-stream@npm:2.6.5"
  dependencies:
    streamx: "npm:^2.21.0"
  peerDependencies:
    bare-buffer: "*"
    bare-events: "*"
  peerDependenciesMeta:
    bare-buffer:
      optional: true
    bare-events:
      optional: true
  checksum: 10c0/1242286f8f3147e9fd353cdaa9cf53226a807ac0dde8177c13f1463aa4cd1f88e07407c883a1b322b901e9af2d1cd30aacd873529031132c384622972e0419df
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.1, base64-js@npm:^1.5.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 10c0/f23823513b63173a001030fae4f2dabe283b99a9d324ade3ad3d148e218134676f1ee8568c877cd79ec1c53158dcf2d2ba527a97c606618928ba99dd930102bf
  languageName: node
  linkType: hard

"bl@npm:^4.0.3":
  version: 4.1.0
  resolution: "bl@npm:4.1.0"
  dependencies:
    buffer: "npm:^5.5.0"
    inherits: "npm:^2.0.4"
    readable-stream: "npm:^3.4.0"
  checksum: 10c0/02847e1d2cb089c9dc6958add42e3cdeaf07d13f575973963335ac0fdece563a50ac770ac4c8fa06492d2dd276f6cc3b7f08c7cd9c7a7ad0f8d388b2a28def5f
  languageName: node
  linkType: hard

"body-parser@npm:1.20.3, body-parser@npm:^1.20.3":
  version: 1.20.3
  resolution: "body-parser@npm:1.20.3"
  dependencies:
    bytes: "npm:3.1.2"
    content-type: "npm:~1.0.5"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    on-finished: "npm:2.4.1"
    qs: "npm:6.13.0"
    raw-body: "npm:2.5.2"
    type-is: "npm:~1.6.18"
    unpipe: "npm:1.0.0"
  checksum: 10c0/0a9a93b7518f222885498dcecaad528cf010dd109b071bf471c93def4bfe30958b83e03496eb9c1ad4896db543d999bb62be1a3087294162a88cfa1b42c16310
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10c0/695a56cd058096a7cb71fb09d9d6a7070113c7be516699ed361317aca2ec169f618e28b8af352e02ab4233fb54eb0168460a40dc320bab0034b36ab59aaad668
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10c0/b358f2fe060e2d7a87aa015979ecea07f3c37d4018f8d6deb5bd4c229ad3a0384fe6029bb76cd8be63c81e516ee52d1a0673edbe2023d53a5191732ae3c3e49f
  languageName: node
  linkType: hard

"braces@npm:^3.0.3":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: "npm:^7.1.1"
  checksum: 10c0/7c6dfd30c338d2997ba77500539227b9d1f85e388a5f43220865201e407e076783d0881f2d297b9f80951b4c957fcf0b51c1d2d24227631643c3f7c284b0aa04
  languageName: node
  linkType: hard

"browserslist@npm:^4.24.0":
  version: 4.25.0
  resolution: "browserslist@npm:4.25.0"
  dependencies:
    caniuse-lite: "npm:^1.0.30001718"
    electron-to-chromium: "npm:^1.5.160"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.3"
  bin:
    browserslist: cli.js
  checksum: 10c0/cc16c55b4468b18684a0e1ca303592b38635b1155d6724f172407192737a2f405b8030d87a05813729592793445b3d15e737b0055f901cdecccb29b1e580a1c5
  languageName: node
  linkType: hard

"buffer@npm:^5.5.0":
  version: 5.7.1
  resolution: "buffer@npm:5.7.1"
  dependencies:
    base64-js: "npm:^1.3.1"
    ieee754: "npm:^1.1.13"
  checksum: 10c0/27cac81cff434ed2876058d72e7c4789d11ff1120ef32c9de48f59eab58179b66710c488987d295ae89a228f835fc66d088652dffeb8e3ba8659f80eb091d55e
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: 10c0/76d1c43cbd602794ad8ad2ae94095cddeb1de78c5dddaa7005c51af10b0176c69971a6d88e805a90c2b6550d76636e43c40d8427a808b8645ede885de4a0358e
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10c0/01f2134e1bd7d3ab68be851df96c8d63b492b1853b67f2eecb2c37bb682d37cb70bb858a16f2f0554d3c0071be6dfe21456a1ff6fa4b7eed996570d6a25ffe9c
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10c0/47bd9901d57b857590431243fea704ff18078b16890a6b3e021e12d279bbf211d039155e27d7566b374d49ee1f8189344bac9833dec7a20cdec370506361c938
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    get-intrinsic: "npm:^1.3.0"
  checksum: 10c0/f4796a6a0941e71c766aea672f63b72bc61234c4f4964dc6d7606e3664c307e7d77845328a8f3359ce39ddb377fed67318f9ee203dea1d47e46165dcf2917644
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 10c0/fff92277400eb06c3079f9e74f3af120db9f8ea03bad0e84d9aede54bbe2d44a56cccb5f6cf12211f93f52306df87077ecec5b712794c5a9b5dac6d615a3f301
  languageName: node
  linkType: hard

"camelcase@npm:6":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 10c0/0d701658219bd3116d12da3eab31acddb3f9440790c0792e0d398f0a520a6a4058018e546862b6fba89d7ae990efaeb97da71e1913e9ebf5a8b5621a3d55c710
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001718":
  version: 1.0.30001721
  resolution: "caniuse-lite@npm:1.0.30001721"
  checksum: 10c0/fa3a8926899824b385279f1f886fe34c5efb1321c9ece1b9df25c8d567a2706db8450cc5b4d969e769e641593e08ea644909324aba93636a43e4949a75f81c4c
  languageName: node
  linkType: hard

"ccount@npm:^2.0.0":
  version: 2.0.1
  resolution: "ccount@npm:2.0.1"
  checksum: 10c0/3939b1664390174484322bc3f45b798462e6c07ee6384cb3d645e0aa2f318502d174845198c1561930e1d431087f74cf1fe291ae9a4722821a9f4ba67e574350
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/4a3fef5cc34975c898ffe77141450f679721df9dde00f6c304353fa9c8b571929123b26a0e4617bde5018977eb655b31970c297b91b63ee83bb82aeb04666880
  languageName: node
  linkType: hard

"character-entities-html4@npm:^2.0.0":
  version: 2.1.0
  resolution: "character-entities-html4@npm:2.1.0"
  checksum: 10c0/fe61b553f083400c20c0b0fd65095df30a0b445d960f3bbf271536ae6c3ba676f39cb7af0b4bf2755812f08ab9b88f2feed68f9aebb73bb153f7a115fe5c6e40
  languageName: node
  linkType: hard

"character-entities-legacy@npm:^3.0.0":
  version: 3.0.0
  resolution: "character-entities-legacy@npm:3.0.0"
  checksum: 10c0/ec4b430af873661aa754a896a2b55af089b4e938d3d010fad5219299a6b6d32ab175142699ee250640678cd64bdecd6db3c9af0b8759ab7b155d970d84c4c7d1
  languageName: node
  linkType: hard

"character-entities@npm:^2.0.0":
  version: 2.0.2
  resolution: "character-entities@npm:2.0.2"
  checksum: 10c0/b0c645a45bcc90ff24f0e0140f4875a8436b8ef13b6bcd31ec02cfb2ca502b680362aa95386f7815bdc04b6464d48cf191210b3840d7c04241a149ede591a308
  languageName: node
  linkType: hard

"character-reference-invalid@npm:^2.0.0":
  version: 2.0.1
  resolution: "character-reference-invalid@npm:2.0.1"
  checksum: 10c0/2ae0dec770cd8659d7e8b0ce24392d83b4c2f0eb4a3395c955dce5528edd4cc030a794cfa06600fcdd700b3f2de2f9b8e40e309c0011c4180e3be64a0b42e6a1
  languageName: node
  linkType: hard

"chownr@npm:^1.1.1":
  version: 1.1.4
  resolution: "chownr@npm:1.1.4"
  checksum: 10c0/ed57952a84cc0c802af900cf7136de643d3aba2eecb59d29344bc2f3f9bf703a301b9d84cdc71f82c3ffc9ccde831b0d92f5b45f91727d6c9da62f23aef9d9db
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10c0/43925b87700f7e3893296c8e9c56cc58f926411cce3a6e5898136daaf08f08b9a8eb76d37d3267e707d0dcc17aed2e2ebdf5848c0c3ce95cf910a919935c1b10
  languageName: node
  linkType: hard

"class-variance-authority@npm:^0.7.1":
  version: 0.7.1
  resolution: "class-variance-authority@npm:0.7.1"
  dependencies:
    clsx: "npm:^2.1.1"
  checksum: 10c0/0f438cea22131808b99272de0fa933c2532d5659773bfec0c583de7b3f038378996d3350683426b8e9c74a6286699382106d71fbec52f0dd5fbb191792cccb5b
  languageName: node
  linkType: hard

"classnames@npm:^2.5.1":
  version: 2.5.1
  resolution: "classnames@npm:2.5.1"
  checksum: 10c0/afff4f77e62cea2d79c39962980bf316bacb0d7c49e13a21adaadb9221e1c6b9d3cdb829d8bb1b23c406f4e740507f37e1dcf506f7e3b7113d17c5bab787aa69
  languageName: node
  linkType: hard

"clsx@npm:^2.1.1":
  version: 2.1.1
  resolution: "clsx@npm:2.1.1"
  checksum: 10c0/c4c8eb865f8c82baab07e71bfa8897c73454881c4f99d6bc81585aecd7c441746c1399d08363dc096c550cceaf97bd4ce1e8854e1771e9998d9f94c4fe075839
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10c0/37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:^1.0.0, color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10c0/a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"color-string@npm:^1.9.0":
  version: 1.9.1
  resolution: "color-string@npm:1.9.1"
  dependencies:
    color-name: "npm:^1.0.0"
    simple-swizzle: "npm:^0.2.2"
  checksum: 10c0/b0bfd74c03b1f837f543898b512f5ea353f71630ccdd0d66f83028d1f0924a7d4272deb278b9aef376cacf1289b522ac3fb175e99895283645a2dc3a33af2404
  languageName: node
  linkType: hard

"color@npm:^4.2.3":
  version: 4.2.3
  resolution: "color@npm:4.2.3"
  dependencies:
    color-convert: "npm:^2.0.1"
    color-string: "npm:^1.9.0"
  checksum: 10c0/7fbe7cfb811054c808349de19fb380252e5e34e61d7d168ec3353e9e9aacb1802674bddc657682e4e9730c2786592a4de6f8283e7e0d3870b829bb0b7b2f6118
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10c0/0dbb829577e1b1e839fa82b40c07ffaf7de8a09b935cadd355a73652ae70a88b4320db322f6634a4ad93424292fa80973ac6480986247f1734a1137debf271d5
  languageName: node
  linkType: hard

"comma-separated-tokens@npm:^2.0.0":
  version: 2.0.3
  resolution: "comma-separated-tokens@npm:2.0.3"
  checksum: 10c0/91f90f1aae320f1755d6957ef0b864fe4f54737f3313bd95e0802686ee2ca38bff1dd381964d00ae5db42912dd1f4ae5c2709644e82706ffc6f6842a813cdd67
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10c0/c996b1cfdf95b6c90fee4dae37e332c8b6eb7d106430c17d538034c0ad9a1630cb194d2ab37293b1bdd4d779494beee7786d586a50bd9376fd6f7bcc2bd4c98f
  languageName: node
  linkType: hard

"console-table-printer@npm:^2.12.1":
  version: 2.14.1
  resolution: "console-table-printer@npm:2.14.1"
  dependencies:
    simple-wcswidth: "npm:^1.0.1"
  checksum: 10c0/5de14abb5c05452be4ede12500bf94fbdc3850a2b448deaf4bb94453a3b25f54df0f37ffc548bd34dc1b9d0eea19af2d8be7ed2c6538ac9912c049039ef35458
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.4":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: "npm:5.2.1"
  checksum: 10c0/bac0316ebfeacb8f381b38285dc691c9939bf0a78b0b7c2d5758acadad242d04783cee5337ba7d12a565a19075af1b3c11c728e1e4946de73c6ff7ce45f3f1bb
  languageName: node
  linkType: hard

"content-type@npm:~1.0.4, content-type@npm:~1.0.5":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: 10c0/b76ebed15c000aee4678c3707e0860cb6abd4e680a598c0a26e17f0bfae723ec9cc2802f0ff1bc6e4d80603719010431d2231018373d4dde10f9ccff9dadf5af
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 10c0/8f2f7a27a1a011cc6cc88cc4da2d7d0cfa5ee0369508baae3d98c260bb3ac520691464e5bbe4ae7cdf09860c1d69ecc6f70c63c6e7c7f7e3f18ec08484dc7d9b
  languageName: node
  linkType: hard

"cookie-parser@npm:^1.4.7":
  version: 1.4.7
  resolution: "cookie-parser@npm:1.4.7"
  dependencies:
    cookie: "npm:0.7.2"
    cookie-signature: "npm:1.0.6"
  checksum: 10c0/46bef553de409031b69a6074ce512d131a98e4fa12612669f1a9c3dd98d56897a31db86a3f4338d4a3a895c6f8d5cfd6fa4d99cdf588e0e8eda655efc3f384dc
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.6":
  version: 1.0.6
  resolution: "cookie-signature@npm:1.0.6"
  checksum: 10c0/b36fd0d4e3fef8456915fcf7742e58fbfcc12a17a018e0eb9501c9d5ef6893b596466f03b0564b81af29ff2538fd0aa4b9d54fe5ccbfb4c90ea50ad29fe2d221
  languageName: node
  linkType: hard

"cookie@npm:0.7.1":
  version: 0.7.1
  resolution: "cookie@npm:0.7.1"
  checksum: 10c0/5de60c67a410e7c8dc8a46a4b72eb0fe925871d057c9a5d2c0e8145c4270a4f81076de83410c4d397179744b478e33cd80ccbcc457abf40a9409ad27dcd21dde
  languageName: node
  linkType: hard

"cookie@npm:0.7.2":
  version: 0.7.2
  resolution: "cookie@npm:0.7.2"
  checksum: 10c0/9596e8ccdbf1a3a88ae02cf5ee80c1c50959423e1022e4e60b91dd87c622af1da309253d8abdb258fb5e3eacb4f08e579dc58b4897b8087574eee0fd35dfa5d2
  languageName: node
  linkType: hard

"copy-to-clipboard@npm:^3.3.1":
  version: 3.3.3
  resolution: "copy-to-clipboard@npm:3.3.3"
  dependencies:
    toggle-selection: "npm:^1.0.6"
  checksum: 10c0/3ebf5e8ee00601f8c440b83ec08d838e8eabb068c1fae94a9cda6b42f288f7e1b552f3463635f419af44bf7675afc8d0390d30876cf5c2d5d35f86d9c56a3e5f
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10c0/053ea8b2135caff68a9e81470e845613e374e7309a47731e81639de3eaeb90c3d01af0e0b44d2ab9d50b43467223b88567dfeb3262db942dc063b9976718ffc1
  languageName: node
  linkType: hard

"css-in-js-utils@npm:^3.1.0":
  version: 3.1.0
  resolution: "css-in-js-utils@npm:3.1.0"
  dependencies:
    hyphenate-style-name: "npm:^1.0.3"
  checksum: 10c0/8bb042e8f7701a7edadc3cce5ce2d5cf41189631d7e2aed194d5a7059b25776dded2a0466cb9da1d1f3fc6c99dcecb51e45671148d073b8a2a71e34755152e52
  languageName: node
  linkType: hard

"css-tree@npm:^1.1.2":
  version: 1.1.3
  resolution: "css-tree@npm:1.1.3"
  dependencies:
    mdn-data: "npm:2.0.14"
    source-map: "npm:^0.6.1"
  checksum: 10c0/499a507bfa39b8b2128f49736882c0dd636b0cd3370f2c69f4558ec86d269113286b7df469afc955de6a68b0dba00bc533e40022a73698081d600072d5d83c1c
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2, csstype@npm:^3.1.2":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 10c0/80c089d6f7e0c5b2bd83cf0539ab41474198579584fa10d86d0cafe0642202343cbc119e076a0b1aece191989477081415d66c9fefbf3c957fc2fc4b7009f248
  languageName: node
  linkType: hard

"debug@npm:2.6.9":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: "npm:2.0.0"
  checksum: 10c0/121908fb839f7801180b69a7e218a40b5a0b718813b886b7d6bdb82001b931c938e2941d1e4450f33a1b1df1da653f5f7a0440c197f29fbf8a6e9d45ff6ef589
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.0.0, debug@npm:^4.1.0, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/d2b44bc1afd912b49bb7ebb0d50a860dc93a4dd7d946e8de94abc957bb63726b7dd5aa48c18c2386c379ec024c46692e15ed3ed97d481729f929201e671fcd55
  languageName: node
  linkType: hard

"decamelize@npm:1.2.0":
  version: 1.2.0
  resolution: "decamelize@npm:1.2.0"
  checksum: 10c0/85c39fe8fbf0482d4a1e224ef0119db5c1897f8503bcef8b826adff7a1b11414972f6fef2d7dec2ee0b4be3863cf64ac1439137ae9e6af23a3d8dcbe26a5b4b2
  languageName: node
  linkType: hard

"decode-named-character-reference@npm:^1.0.0":
  version: 1.1.0
  resolution: "decode-named-character-reference@npm:1.1.0"
  dependencies:
    character-entities: "npm:^2.0.0"
  checksum: 10c0/359c76305b47e67660ec096c5cd3f65972ed75b8a53a40435a7a967cfab3e9516e64b443cbe0c7edcf5ab77f65a6924f12fb1872b1e09e2f044f28f4fd10996a
  languageName: node
  linkType: hard

"decompress-response@npm:^6.0.0":
  version: 6.0.0
  resolution: "decompress-response@npm:6.0.0"
  dependencies:
    mimic-response: "npm:^3.1.0"
  checksum: 10c0/bd89d23141b96d80577e70c54fb226b2f40e74a6817652b80a116d7befb8758261ad073a8895648a29cc0a5947021ab66705cb542fa9c143c82022b27c5b175e
  languageName: node
  linkType: hard

"deep-extend@npm:^0.6.0":
  version: 0.6.0
  resolution: "deep-extend@npm:0.6.0"
  checksum: 10c0/1c6b0abcdb901e13a44c7d699116d3d4279fdb261983122a3783e7273844d5f2537dc2e1c454a23fcf645917f93fbf8d07101c1d03c015a87faa662755212566
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: 10c0/7f0ee496e0dff14a573dc6127f14c95061b448b87b995fc96c017ce0a1e66af1675e73f1d6064407975bc4ea6ab679497a29fff7b5b9c4e99cb10797c1ad0b4c
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10c0/d758899da03392e6712f042bec80aa293bbe9e9ff1b2634baae6a360113e708b91326594c8a486d475c69d6259afb7efacdc3537bfcda1c6c648e390ce601b19
  languageName: node
  linkType: hard

"depd@npm:2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: 10c0/58bd06ec20e19529b06f7ad07ddab60e504d9e0faca4bd23079fac2d279c3594334d736508dc350e06e510aba5e22e4594483b3a6562ce7c17dd797f4cc4ad2c
  languageName: node
  linkType: hard

"dequal@npm:^2.0.0":
  version: 2.0.3
  resolution: "dequal@npm:2.0.3"
  checksum: 10c0/f98860cdf58b64991ae10205137c0e97d384c3a4edc7f807603887b7c4b850af1224a33d88012009f150861cbee4fa2d322c4cc04b9313bee312e47f6ecaa888
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 10c0/bd7633942f57418f5a3b80d5cb53898127bcf53e24cdf5d5f4396be471417671f0fee48a4ebe9a1e9defbde2a31280011af58a57e090ff822f589b443ed4e643
  languageName: node
  linkType: hard

"detect-libc@npm:^2.0.0, detect-libc@npm:^2.0.2, detect-libc@npm:^2.0.3, detect-libc@npm:^2.0.4":
  version: 2.0.4
  resolution: "detect-libc@npm:2.0.4"
  checksum: 10c0/c15541f836eba4b1f521e4eecc28eefefdbc10a94d3b8cb4c507689f332cc111babb95deda66f2de050b22122113189986d5190be97d51b5a2b23b938415e67c
  languageName: node
  linkType: hard

"detect-node-es@npm:^1.1.0":
  version: 1.1.0
  resolution: "detect-node-es@npm:1.1.0"
  checksum: 10c0/e562f00de23f10c27d7119e1af0e7388407eb4b06596a25f6d79a360094a109ff285de317f02b090faae093d314cf6e73ac3214f8a5bb3a0def5bece94557fbe
  languageName: node
  linkType: hard

"devlop@npm:^1.0.0, devlop@npm:^1.1.0":
  version: 1.1.0
  resolution: "devlop@npm:1.1.0"
  dependencies:
    dequal: "npm:^2.0.0"
  checksum: 10c0/e0928ab8f94c59417a2b8389c45c55ce0a02d9ac7fd74ef62d01ba48060129e1d594501b77de01f3eeafc7cb00773819b0df74d96251cf20b31c5b3071f45c0e
  languageName: node
  linkType: hard

"dotenv@npm:^16.4.7":
  version: 16.5.0
  resolution: "dotenv@npm:16.5.0"
  checksum: 10c0/5bc94c919fbd955bf0ba44d33922a1e93d1078e64a1db5c30faeded1d996e7a83c55332cb8ea4fae5a9ca4d0be44cbceb95c5811e70f9f095298df09d1997dd9
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10c0/199f2a0c1c16593ca0a145dbf76a962f8033ce3129f01284d48c45ed4e14fea9bbacd7b3610b6cdc33486cef20385ac054948fefc6272fcce645c09468f93031
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10c0/26f364ebcdb6395f95124fda411f63137a4bfb5d3a06453f7f23dfe52502905bd84e0488172e0f9ec295fdc45f05c23d5d91baf16bd26f0fe9acd777a188dc39
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 10c0/b5bb125ee93161bc16bfe6e56c6b04de5ad2aa44234d8f644813cc95d861a6910903132b05093706de2b706599367c4130eb6d170f6b46895686b95f87d017b7
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.160":
  version: 1.5.165
  resolution: "electron-to-chromium@npm:1.5.165"
  checksum: 10c0/20b91e67e7a8829a358c4a488e9b59b0e5f8d4cb075a70b9757bb21acf0fc751ca58ca7d9c6018bec74ac4bd42f7859e4ef37421c252a2275f642e12a32271d6
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10c0/b6053ad39951c4cf338f9092d7bfba448cdfd46fe6a2a034700b149ac9ffbc137e361cbd3c442297f86bed2e5f7576c1b54cc0a6bf8ef5106cc62f496af35010
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10c0/af014e759a72064cf66e6e694a7fc6b0ed3d8db680427b021a89727689671cefe9d04151b2cad51dbaf85d5ba790d061cd167f1cf32eb7b281f6368b3c181639
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: 10c0/f6c2387379a9e7c1156c1c3d4f9cb7bb11cf16dd4c1682e1f6746512564b053df5781029b6061296832b59fb22f459dbe250386d217c2f6e203601abb2ee0bec
  languageName: node
  linkType: hard

"encodeurl@npm:~2.0.0":
  version: 2.0.0
  resolution: "encodeurl@npm:2.0.0"
  checksum: 10c0/5d317306acb13e6590e28e27924c754163946a2480de11865c991a3a7eed4315cd3fba378b543ca145829569eefe9b899f3d84bb09870f675ae60bc924b01ceb
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10c0/36d938712ff00fe1f4bac88b43bcffb5930c1efa57bbcdca9d67e1d9d6c57cfb1200fb01efe0f3109b2ce99b231f90779532814a81370a1bd3274a0f58585039
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.1.0, end-of-stream@npm:^1.4.1":
  version: 1.4.4
  resolution: "end-of-stream@npm:1.4.4"
  dependencies:
    once: "npm:^1.4.0"
  checksum: 10c0/870b423afb2d54bb8d243c63e07c170409d41e20b47eeef0727547aea5740bd6717aca45597a9f2745525667a6b804c1e7bede41f856818faee5806dd9ff3975
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^5.18.1":
  version: 5.18.1
  resolution: "enhanced-resolve@npm:5.18.1"
  dependencies:
    graceful-fs: "npm:^4.2.4"
    tapable: "npm:^2.2.0"
  checksum: 10c0/4cffd9b125225184e2abed9fdf0ed3dbd2224c873b165d0838fd066cde32e0918626cba2f1f4bf6860762f13a7e2364fd89a82b99566be2873d813573ac71846
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10c0/285325677bf00e30845e330eec32894f5105529db97496ee3f598478e50f008c5352a41a30e5e72ec9de8a542b5a570b85699cd63bd2bc646dbcb9f311d83bc4
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10c0/b642f7b4dd4a376e954947550a3065a9ece6733ab8e51ad80db727aaae0817c2e99b02a97a3d6cecc648a97848305e728289cf312d09af395403a90c9d4d8a66
  languageName: node
  linkType: hard

"error-stack-parser@npm:^2.0.6":
  version: 2.1.4
  resolution: "error-stack-parser@npm:2.1.4"
  dependencies:
    stackframe: "npm:^1.3.4"
  checksum: 10c0/7679b780043c98b01fc546725484e0cfd3071bf5c906bbe358722972f04abf4fc3f0a77988017665bab367f6ef3fc2d0185f7528f45966b83e7c99c02d5509b9
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10c0/3f54eb49c16c18707949ff25a1456728c883e81259f045003499efba399c08bad00deebf65cccde8c0e07908c1a225c9d472b7107e558f2a48e28d530e34527c
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10c0/0a61325670072f98d8ae3b914edab3559b6caa980f08054a3b872052640d91da01d38df55df797fcc916389d77fc92b8d5906cf028f4db46d7e3003abecbca85
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10c0/65364812ca4daf48eb76e2a3b7a89b3f6a2e62a1c420766ce9f692665a29d94fe41fe88b65f24106f449859549711e4b40d9fb8002d862dfd7eb1c512d10be0c
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10c0/ef2ca9ce49afe3931cb32e35da4dcb6d86ab02592cfc2ce3e49ced199d9d0bb5085fc7e73e06312213765f5efa47cc1df553a6a5154584b21448e9fb8355b1af
  languageName: node
  linkType: hard

"esbuild@npm:^0.25.0":
  version: 0.25.5
  resolution: "esbuild@npm:0.25.5"
  dependencies:
    "@esbuild/aix-ppc64": "npm:0.25.5"
    "@esbuild/android-arm": "npm:0.25.5"
    "@esbuild/android-arm64": "npm:0.25.5"
    "@esbuild/android-x64": "npm:0.25.5"
    "@esbuild/darwin-arm64": "npm:0.25.5"
    "@esbuild/darwin-x64": "npm:0.25.5"
    "@esbuild/freebsd-arm64": "npm:0.25.5"
    "@esbuild/freebsd-x64": "npm:0.25.5"
    "@esbuild/linux-arm": "npm:0.25.5"
    "@esbuild/linux-arm64": "npm:0.25.5"
    "@esbuild/linux-ia32": "npm:0.25.5"
    "@esbuild/linux-loong64": "npm:0.25.5"
    "@esbuild/linux-mips64el": "npm:0.25.5"
    "@esbuild/linux-ppc64": "npm:0.25.5"
    "@esbuild/linux-riscv64": "npm:0.25.5"
    "@esbuild/linux-s390x": "npm:0.25.5"
    "@esbuild/linux-x64": "npm:0.25.5"
    "@esbuild/netbsd-arm64": "npm:0.25.5"
    "@esbuild/netbsd-x64": "npm:0.25.5"
    "@esbuild/openbsd-arm64": "npm:0.25.5"
    "@esbuild/openbsd-x64": "npm:0.25.5"
    "@esbuild/sunos-x64": "npm:0.25.5"
    "@esbuild/win32-arm64": "npm:0.25.5"
    "@esbuild/win32-ia32": "npm:0.25.5"
    "@esbuild/win32-x64": "npm:0.25.5"
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-arm64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-arm64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 10c0/aba8cbc11927fa77562722ed5e95541ce2853f67ad7bdc40382b558abc2e0ec57d92ffb820f082ba2047b4ef9f3bc3da068cdebe30dfd3850cfa3827a78d604e
  languageName: node
  linkType: hard

"escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 10c0/ced4dd3a78e15897ed3be74e635110bbf3b08877b0a41be50dcb325ee0e0b5f65fc2d50e9845194d7c4633f327e2e1c6cce00a71b617c5673df0374201d67f65
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 10c0/524c739d776b36c3d29fa08a22e03e8824e3b2fd57500e5e44ecf3cc4707c34c60f9ca0781c0e33d191f2991161504c295e98f68c78fe7baa6e57081ec6ac0a3
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 10c0/9497d4dd307d845bd7f75180d8188bb17ea8c151c1edbf6b6717c100e104d629dc2dfb687686181b0f4b7d732c7dfdc4d5e7a8ff72de1b0ca283a75bbb3a9cd9
  languageName: node
  linkType: hard

"eslint-plugin-react-hooks@npm:^5.1.0":
  version: 5.2.0
  resolution: "eslint-plugin-react-hooks@npm:5.2.0"
  peerDependencies:
    eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0
  checksum: 10c0/1c8d50fa5984c6dea32470651807d2922cc3934cf3425e78f84a24c2dfd972e7f019bee84aefb27e0cf2c13fea0ac1d4473267727408feeb1c56333ca1489385
  languageName: node
  linkType: hard

"eslint-plugin-react-refresh@npm:^0.4.19":
  version: 0.4.20
  resolution: "eslint-plugin-react-refresh@npm:0.4.20"
  peerDependencies:
    eslint: ">=8.40"
  checksum: 10c0/2ccf4ba28f1dcbcb9e773e46eae1e61e568bba69281a700eb26fd762152e4e90a78c991f9c8173342a7cd2a82f3f52fedb40a1e81360cef9c40ea5b814fa3613
  languageName: node
  linkType: hard

"eslint-scope@npm:^8.3.0":
  version: 8.3.0
  resolution: "eslint-scope@npm:8.3.0"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^5.2.0"
  checksum: 10c0/23bf54345573201fdf06d29efa345ab508b355492f6c6cc9e2b9f6d02b896f369b6dd5315205be94b8853809776c4d13353b85c6b531997b164ff6c3328ecf5b
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 10c0/92708e882c0a5ffd88c23c0b404ac1628cf20104a108c745f240a13c332a11aac54f49a22d5762efbffc18ecbc9a580d1b7ad034bf5f3cc3307e5cbff2ec9820
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^4.2.0":
  version: 4.2.0
  resolution: "eslint-visitor-keys@npm:4.2.0"
  checksum: 10c0/2ed81c663b147ca6f578312919483eb040295bbab759e5a371953456c636c5b49a559883e2677112453728d66293c0a4c90ab11cab3428cf02a0236d2e738269
  languageName: node
  linkType: hard

"eslint@npm:^9.21.0":
  version: 9.28.0
  resolution: "eslint@npm:9.28.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.2.0"
    "@eslint-community/regexpp": "npm:^4.12.1"
    "@eslint/config-array": "npm:^0.20.0"
    "@eslint/config-helpers": "npm:^0.2.1"
    "@eslint/core": "npm:^0.14.0"
    "@eslint/eslintrc": "npm:^3.3.1"
    "@eslint/js": "npm:9.28.0"
    "@eslint/plugin-kit": "npm:^0.3.1"
    "@humanfs/node": "npm:^0.16.6"
    "@humanwhocodes/module-importer": "npm:^1.0.1"
    "@humanwhocodes/retry": "npm:^0.4.2"
    "@types/estree": "npm:^1.0.6"
    "@types/json-schema": "npm:^7.0.15"
    ajv: "npm:^6.12.4"
    chalk: "npm:^4.0.0"
    cross-spawn: "npm:^7.0.6"
    debug: "npm:^4.3.2"
    escape-string-regexp: "npm:^4.0.0"
    eslint-scope: "npm:^8.3.0"
    eslint-visitor-keys: "npm:^4.2.0"
    espree: "npm:^10.3.0"
    esquery: "npm:^1.5.0"
    esutils: "npm:^2.0.2"
    fast-deep-equal: "npm:^3.1.3"
    file-entry-cache: "npm:^8.0.0"
    find-up: "npm:^5.0.0"
    glob-parent: "npm:^6.0.2"
    ignore: "npm:^5.2.0"
    imurmurhash: "npm:^0.1.4"
    is-glob: "npm:^4.0.0"
    json-stable-stringify-without-jsonify: "npm:^1.0.1"
    lodash.merge: "npm:^4.6.2"
    minimatch: "npm:^3.1.2"
    natural-compare: "npm:^1.4.0"
    optionator: "npm:^0.9.3"
  peerDependencies:
    jiti: "*"
  peerDependenciesMeta:
    jiti:
      optional: true
  bin:
    eslint: bin/eslint.js
  checksum: 10c0/513ea7e69d88a0905d4ed35cef3a8f31ebce7ca9f2cdbda3474495c63ad6831d52357aad65094be7a144d6e51850980ced7d25efb807e8ab06a427241f7cd730
  languageName: node
  linkType: hard

"espree@npm:^10.0.1, espree@npm:^10.3.0":
  version: 10.3.0
  resolution: "espree@npm:10.3.0"
  dependencies:
    acorn: "npm:^8.14.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^4.2.0"
  checksum: 10c0/272beeaca70d0a1a047d61baff64db04664a33d7cfb5d144f84bc8a5c6194c6c8ebe9cc594093ca53add88baa23e59b01e69e8a0160ab32eac570482e165c462
  languageName: node
  linkType: hard

"esquery@npm:^1.5.0":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: "npm:^5.1.0"
  checksum: 10c0/cb9065ec605f9da7a76ca6dadb0619dfb611e37a81e318732977d90fab50a256b95fee2d925fba7c2f3f0523aa16f91587246693bc09bc34d5a59575fe6e93d2
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: "npm:^5.2.0"
  checksum: 10c0/81a37116d1408ded88ada45b9fb16dbd26fba3aadc369ce50fcaf82a0bac12772ebd7b24cd7b91fc66786bf2c1ac7b5f196bc990a473efff972f5cb338877cf5
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 10c0/1ff9447b96263dec95d6d67431c5e0771eb9776427421260a3e2f0fdd5d6bd4f8e37a7338f5ad2880c9f143450c9b1e4fc2069060724570a49cf9cf0312bd107
  languageName: node
  linkType: hard

"estree-util-is-identifier-name@npm:^3.0.0":
  version: 3.0.0
  resolution: "estree-util-is-identifier-name@npm:3.0.0"
  checksum: 10c0/d1881c6ed14bd588ebd508fc90bf2a541811dbb9ca04dec2f39d27dcaa635f85b5ed9bbbe7fc6fb1ddfca68744a5f7c70456b4b7108b6c4c52780631cc787c5b
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 10c0/9a2fe69a41bfdade834ba7c42de4723c97ec776e40656919c62cbd13607c45e127a003f05f724a1ea55e5029a4cf2de444b13009f2af71271e42d93a637137c7
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 10c0/12be11ef62fb9817314d790089a0a49fae4e1b50594135dcb8076312b7d7e470884b5100d249b28c18581b7fd52f8b485689ffae22a11ed9ec17377a33a08f84
  languageName: node
  linkType: hard

"event-target-shim@npm:^5.0.0":
  version: 5.0.1
  resolution: "event-target-shim@npm:5.0.1"
  checksum: 10c0/0255d9f936215fd206156fd4caa9e8d35e62075d720dc7d847e89b417e5e62cf1ce6c9b4e0a1633a9256de0efefaf9f8d26924b1f3c8620cffb9db78e7d3076b
  languageName: node
  linkType: hard

"eventemitter3@npm:^4.0.4":
  version: 4.0.7
  resolution: "eventemitter3@npm:4.0.7"
  checksum: 10c0/5f6d97cbcbac47be798e6355e3a7639a84ee1f7d9b199a07017f1d2f1e2fe236004d14fa5dfaeba661f94ea57805385e326236a6debbc7145c8877fbc0297c6b
  languageName: node
  linkType: hard

"expand-template@npm:^2.0.3":
  version: 2.0.3
  resolution: "expand-template@npm:2.0.3"
  checksum: 10c0/1c9e7afe9acadf9d373301d27f6a47b34e89b3391b1ef38b7471d381812537ef2457e620ae7f819d2642ce9c43b189b3583813ec395e2938319abe356a9b2f51
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 10c0/d9d3e1eafa21b78464297df91f1776f7fbaa3d5e3f7f0995648ca5b89c069d17055033817348d9f4a43d1c20b0eab84f75af6991751e839df53e4dfd6f22e844
  languageName: node
  linkType: hard

"express@npm:^4.21.2":
  version: 4.21.2
  resolution: "express@npm:4.21.2"
  dependencies:
    accepts: "npm:~1.3.8"
    array-flatten: "npm:1.1.1"
    body-parser: "npm:1.20.3"
    content-disposition: "npm:0.5.4"
    content-type: "npm:~1.0.4"
    cookie: "npm:0.7.1"
    cookie-signature: "npm:1.0.6"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    finalhandler: "npm:1.3.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    merge-descriptors: "npm:1.0.3"
    methods: "npm:~1.1.2"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    path-to-regexp: "npm:0.1.12"
    proxy-addr: "npm:~2.0.7"
    qs: "npm:6.13.0"
    range-parser: "npm:~1.2.1"
    safe-buffer: "npm:5.2.1"
    send: "npm:0.19.0"
    serve-static: "npm:1.16.2"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    type-is: "npm:~1.6.18"
    utils-merge: "npm:1.0.1"
    vary: "npm:~1.1.2"
  checksum: 10c0/38168fd0a32756600b56e6214afecf4fc79ec28eca7f7a91c2ab8d50df4f47562ca3f9dee412da7f5cea6b1a1544b33b40f9f8586dbacfbdada0fe90dbb10a1f
  languageName: node
  linkType: hard

"extend@npm:^3.0.0":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: 10c0/73bf6e27406e80aa3e85b0d1c4fd987261e628064e170ca781125c0b635a3dabad5e05adbf07595ea0cf1e6c5396cacb214af933da7cbaf24fe75ff14818e8f9
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10c0/40dedc862eb8992c54579c66d914635afbec43350afbbe991235fdcb4e3a8d5af1b23ae7e79bef7d4882d0ecee06c3197488026998fb19f72dc95acff1d1b1d0
  languageName: node
  linkType: hard

"fast-fifo@npm:^1.2.0, fast-fifo@npm:^1.3.2":
  version: 1.3.2
  resolution: "fast-fifo@npm:1.3.2"
  checksum: 10c0/d53f6f786875e8b0529f784b59b4b05d4b5c31c651710496440006a398389a579c8dbcd2081311478b5bf77f4b0b21de69109c5a4eabea9d8e8783d1eb864e4c
  languageName: node
  linkType: hard

"fast-glob@npm:^3.3.2":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.8"
  checksum: 10c0/f6aaa141d0d3384cf73cbcdfc52f475ed293f6d5b65bfc5def368b09163a9f7e5ec2b3014d80f733c405f58e470ee0cc451c2937685045cddcdeaa24199c43fe
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 10c0/7f081eb0b8a64e0057b3bb03f974b3ef00135fbf36c1c710895cd9300f13c94ba809bb3a81cf4e1b03f6e5285610a61abbd7602d0652de423144dfee5a389c9b
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 10c0/111972b37338bcb88f7d9e2c5907862c280ebf4234433b95bc611e518d192ccb2d38119c4ac86e26b668d75f7f3894f4ff5c4982899afced7ca78633b08287c4
  languageName: node
  linkType: hard

"fast-shallow-equal@npm:^1.0.0":
  version: 1.0.0
  resolution: "fast-shallow-equal@npm:1.0.0"
  checksum: 10c0/526c393c011ab5a0ca5a36c5ea25c9730acd027503ccbec6c7825397ab9375f51f67f14c8829b4c4b1ccccede695391dd14863a15e40a37fc4af08c1440a1b66
  languageName: node
  linkType: hard

"fast-xml-parser@npm:^4.4.1":
  version: 4.5.3
  resolution: "fast-xml-parser@npm:4.5.3"
  dependencies:
    strnum: "npm:^1.1.1"
  bin:
    fxparser: src/cli/cli.js
  checksum: 10c0/bf9ccadacfadc95f6e3f0e7882a380a7f219cf0a6f96575149f02cb62bf44c3b7f0daee75b8ff3847bcfd7fbcb201e402c71045936c265cf6d94b141ec4e9327
  languageName: node
  linkType: hard

"fastest-stable-stringify@npm:^2.0.2":
  version: 2.0.2
  resolution: "fastest-stable-stringify@npm:2.0.2"
  checksum: 10c0/abbe5ff48f13f5819e7312dbb38bae5d9960694cffd315b464df9adcd02a8fa7e9eec32c314655674c7134905c544b7a0c14b05bfbe30b3f678609bebc9fecb9
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.19.1
  resolution: "fastq@npm:1.19.1"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10c0/ebc6e50ac7048daaeb8e64522a1ea7a26e92b3cee5cd1c7f2316cdca81ba543aa40a136b53891446ea5c3a67ec215fbaca87ad405f102dd97012f62916905630
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4":
  version: 6.4.5
  resolution: "fdir@npm:6.4.5"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 10c0/5d63330a1b97165e9b0fb20369fafc7cf826bc4b3e374efcb650bc77d7145ac01193b5da1a7591eab89ae6fd6b15cdd414085910b2a2b42296b1480c9f2677af
  languageName: node
  linkType: hard

"file-entry-cache@npm:^8.0.0":
  version: 8.0.0
  resolution: "file-entry-cache@npm:8.0.0"
  dependencies:
    flat-cache: "npm:^4.0.0"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10c0/b75b691bbe065472f38824f694c2f7449d7f5004aa950426a2c28f0306c60db9b880c0b0e4ed819997ffb882d1da02cfcfc819bddc94d71627f5269682edf018
  languageName: node
  linkType: hard

"finalhandler@npm:1.3.1":
  version: 1.3.1
  resolution: "finalhandler@npm:1.3.1"
  dependencies:
    debug: "npm:2.6.9"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    statuses: "npm:2.0.1"
    unpipe: "npm:~1.0.0"
  checksum: 10c0/d38035831865a49b5610206a3a9a9aae4e8523cbbcd01175d0480ffbf1278c47f11d89be3ca7f617ae6d94f29cf797546a4619cd84dd109009ef33f12f69019f
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: "npm:^6.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10c0/062c5a83a9c02f53cdd6d175a37ecf8f87ea5bbff1fdfb828f04bfa021441bc7583e8ebc0872a4c1baab96221fb8a8a275a19809fb93fbc40bd69ec35634069a
  languageName: node
  linkType: hard

"flat-cache@npm:^4.0.0":
  version: 4.0.1
  resolution: "flat-cache@npm:4.0.1"
  dependencies:
    flatted: "npm:^3.2.9"
    keyv: "npm:^4.5.4"
  checksum: 10c0/2c59d93e9faa2523e4fda6b4ada749bed432cfa28c8e251f33b25795e426a1c6dbada777afb1f74fcfff33934fdbdea921ee738fcc33e71adc9d6eca984a1cfc
  languageName: node
  linkType: hard

"flatbuffers@npm:^1.12.0":
  version: 1.12.0
  resolution: "flatbuffers@npm:1.12.0"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.3
  resolution: "flatted@npm:3.3.3"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: "npm:^7.0.6"
    signal-exit: "npm:^4.0.1"
  checksum: 10c0/8986e4af2430896e65bc2788d6679067294d6aee9545daefc84923a0a4b399ad9c7a3ea7bd8c0b2b80fdf4a92de4c69df3f628233ff3224260e9c1541a9e9ed3
  languageName: node
  linkType: hard

"form-data-encoder@npm:1.7.2":
  version: 1.7.2
  resolution: "form-data-encoder@npm:1.7.2"
  checksum: 10c0/56553768037b6d55d9de524f97fe70555f0e415e781cb56fc457a68263de3d40fadea2304d4beef2d40b1a851269bd7854e42c362107071892cb5238debe9464
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.3
  resolution: "form-data@npm:4.0.3"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    es-set-tostringtag: "npm:^2.1.0"
    hasown: "npm:^2.0.2"
    mime-types: "npm:^2.1.12"
  checksum: 10c0/f0cf45873d600110b5fadf5804478377694f73a1ed97aaa370a74c90cebd7fe6e845a081171668a5476477d0d55a73a4e03d6682968fa8661eac2a81d651fcdb
  languageName: node
  linkType: hard

"formdata-node@npm:^4.3.2":
  version: 4.4.1
  resolution: "formdata-node@npm:4.4.1"
  dependencies:
    node-domexception: "npm:1.0.0"
    web-streams-polyfill: "npm:4.0.0-beta.3"
  checksum: 10c0/74151e7b228ffb33b565cec69182694ad07cc3fdd9126a8240468bb70a8ba66e97e097072b60bcb08729b24c7ce3fd3e0bd7f1f80df6f9f662b9656786e76f6a
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: 10c0/9b67c3fac86acdbc9ae47ba1ddd5f2f81526fa4c8226863ede5600a3f7c7416ef451f6f1e240a3cc32d0fd79fcfe6beb08fd0da454f360032bde70bf80afbb33
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 10c0/c6d27f3ed86cc5b601404822f31c900dd165ba63fff8152a3ef714e2012e7535027063bc67ded4cb5b3a49fa596495d46cacd9f47d6328459cf570f08b7d9e5a
  languageName: node
  linkType: hard

"fs-constants@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs-constants@npm:1.0.0"
  checksum: 10c0/a0cde99085f0872f4d244e83e03a46aa387b74f5a5af750896c6b05e9077fac00e9932fdf5aef84f2f16634cd473c63037d7a512576da7d5c2b9163d1909f3a8
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/63e80da2ff9b621e2cb1596abcb9207f1cf82b968b116ccd7b959e3323144cce7fb141462200971c38bbf2ecca51695069db45265705bed09a7cd93ae5b89f94
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2, fsevents@npm:~2.3.3":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/a1f0c44595123ed717febbc478aa952e47adfc28e2092be66b8ab1635147254ca6cfe1df792a8997f22716d4cbafc73309899ff7bfac2ac3ad8cf2e4ecc3ec60
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>, fsevents@patch:fsevents@npm%3A~2.3.3#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10c0/d8680ee1e5fcd4c197e4ac33b2b4dce03c71f4d91717292785703db200f5c21f977c568d28061226f9b5900cbcd2c84463646134fd5337e7925e0942bc3f46d5
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: 10c0/782aba6cba65b1bb5af3b095d96249d20edbe8df32dbf4696fd49be2583faf676173bf4809386588828e4dd76a3354fcbeb577bab1c833ccd9fc4577f26103f8
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10c0/52c81808af9a8130f581e6a6a83e1ba4a9f703359e7a438d1369a5267a25412322f03dcbd7c549edaef0b6214a0630a28511d7df0130c93cfd380f4fa0b5b66a
  languageName: node
  linkType: hard

"get-nonce@npm:^1.0.0":
  version: 1.0.1
  resolution: "get-nonce@npm:1.0.1"
  checksum: 10c0/2d7df55279060bf0568549e1ffc9b84bc32a32b7541675ca092dce56317cdd1a59a98dcc4072c9f6a980779440139a3221d7486f52c488e69dc0fd27b1efb162
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/9224acb44603c5526955e83510b9da41baf6ae73f7398875fba50edc5e944223a89c4a72b070fcd78beb5f7bdda58ecb6294adc28f7acfc0da05f76a2399643c
  languageName: node
  linkType: hard

"github-from-package@npm:0.0.0":
  version: 0.0.0
  resolution: "github-from-package@npm:0.0.0"
  checksum: 10c0/737ee3f52d0a27e26332cde85b533c21fcdc0b09fb716c3f8e522cfaa9c600d4a631dec9fcde179ec9d47cca89017b7848ed4d6ae6b6b78f936c06825b1fcc12
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10c0/cab87638e2112bee3f839ef5f6e0765057163d39c66be8ec1602f3823da4692297ad4e972de876ea17c44d652978638d2fd583c6713d0eb6591706825020c9ee
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: "npm:^4.0.3"
  checksum: 10c0/317034d88654730230b3f43bb7ad4f7c90257a426e872ea0bf157473ac61c99bf5d205fad8f0185f989be8d2fa6d3c7dce1645d99d545b6ea9089c39f838e7f8
  languageName: node
  linkType: hard

"glob@npm:^10.2.2":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10c0/19a9759ea77b8e3ca0a43c2f07ecddc2ad46216b786bb8f993c445aee80d345925a21e5280c7b7c6c59e860a0154b84e4b2b60321fea92cd3c56b4a7489f160e
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 10c0/758f9f258e7b19226bd8d4af5d3b0dcf7038780fb23d82e6f98932c44e239f884847f1766e8fa9cc5635ccb3204f7fa7314d4408dd4002a5e8ea827b4018f0a1
  languageName: node
  linkType: hard

"globals@npm:^14.0.0":
  version: 14.0.0
  resolution: "globals@npm:14.0.0"
  checksum: 10c0/b96ff42620c9231ad468d4c58ff42afee7777ee1c963013ff8aabe095a451d0ceeb8dcd8ef4cbd64d2538cef45f787a78ba3a9574f4a634438963e334471302d
  languageName: node
  linkType: hard

"globals@npm:^15.15.0":
  version: 15.15.0
  resolution: "globals@npm:15.15.0"
  checksum: 10c0/f9ae80996392ca71316495a39bec88ac43ae3525a438b5626cd9d5ce9d5500d0a98a266409605f8cd7241c7acf57c354a48111ea02a767ba4f374b806d6861fe
  languageName: node
  linkType: hard

"gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10c0/50fff1e04ba2b7737c097358534eacadad1e68d24cccee3272e04e007bed008e68d2614f3987788428fd192a5ae3889d08fb2331417e4fc4a9ab366b2043cead
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10c0/386d011a553e02bc594ac2ca0bd6d9e4c22d7fa8cfbfc448a6d148c59ea881b092db9dbe3547ae4b88e55f1b01f7c4a2ecc53b310c042793e63aa44cf6c257f2
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: 10c0/e951259d8cd2e0d196c72ec711add7115d42eb9a8146c8eeda5b8d3ac91e5dd816b9cd68920726d9fd4490368e7ed86e9c423f40db87e2d8dfafa00fa17c3a31
  languageName: node
  linkType: hard

"guid-typescript@npm:^1.0.9":
  version: 1.0.9
  resolution: "guid-typescript@npm:1.0.9"
  checksum: 10c0/fa0a2b2b4e06e0976a81c947b74e114b92f6647e84b52e24ab0981d2fdbaa1a640641d8fd269004dd7c581baebeb4f9d9782b74391e717e47c9b822bea4b3be6
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10c0/2e789c61b7888d66993e14e8331449e525ef42aac53c627cc53d1c3334e768bcb6abdc4f5f0de1478a25beec6f0bd62c7549058b7ac53e924040d4f301f02fd1
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10c0/dde0a734b17ae51e84b10986e651c664379018d10b91b6b0e9b293eddb32f0f069688c841fb40f19e9611546130153e0a2a48fd7f512891fb000ddfa36f5a20e
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: 10c0/a8b166462192bafe3d9b6e420a1d581d93dd867adb61be223a17a8d6dad147aa77a8be32c961bb2f27b3ef893cae8d36f564ab651f5e9b7938ae86f74027c48c
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10c0/3769d434703b8ac66b209a4cca0737519925bbdb61dd887f93a16372b14694c63ff4e797686d87c90f08168e81082248b9b028bad60d4da9e0d1148766f56eb9
  languageName: node
  linkType: hard

"hast-util-to-jsx-runtime@npm:^2.0.0":
  version: 2.3.6
  resolution: "hast-util-to-jsx-runtime@npm:2.3.6"
  dependencies:
    "@types/estree": "npm:^1.0.0"
    "@types/hast": "npm:^3.0.0"
    "@types/unist": "npm:^3.0.0"
    comma-separated-tokens: "npm:^2.0.0"
    devlop: "npm:^1.0.0"
    estree-util-is-identifier-name: "npm:^3.0.0"
    hast-util-whitespace: "npm:^3.0.0"
    mdast-util-mdx-expression: "npm:^2.0.0"
    mdast-util-mdx-jsx: "npm:^3.0.0"
    mdast-util-mdxjs-esm: "npm:^2.0.0"
    property-information: "npm:^7.0.0"
    space-separated-tokens: "npm:^2.0.0"
    style-to-js: "npm:^1.0.0"
    unist-util-position: "npm:^5.0.0"
    vfile-message: "npm:^4.0.0"
  checksum: 10c0/27297e02848fe37ef219be04a26ce708d17278a175a807689e94a821dcffc88aa506d62c3a85beed1f9a8544f7211bdcbcde0528b7b456a57c2e342c3fd11056
  languageName: node
  linkType: hard

"hast-util-whitespace@npm:^3.0.0":
  version: 3.0.0
  resolution: "hast-util-whitespace@npm:3.0.0"
  dependencies:
    "@types/hast": "npm:^3.0.0"
  checksum: 10c0/b898bc9fe27884b272580d15260b6bbdabe239973a147e97fa98c45fa0ffec967a481aaa42291ec34fb56530dc2d484d473d7e2bae79f39c83f3762307edfea8
  languageName: node
  linkType: hard

"html-space-editor@workspace:.":
  version: 0.0.0-use.local
  resolution: "html-space-editor@workspace:."
  dependencies:
    "@eslint/js": "npm:^9.21.0"
    "@google/generative-ai": "npm:^0.21.0"
    "@huggingface/hub": "npm:^1.1.1"
    "@huggingface/inference": "npm:^4.0.2"
    "@langchain/anthropic": "npm:^0.3.21"
    "@langchain/core": "npm:^0.3.57"
    "@langchain/langgraph": "npm:^0.3.1"
    "@langchain/openai": "npm:^0.5.12"
    "@monaco-editor/react": "npm:^4.7.0"
    "@radix-ui/react-avatar": "npm:^1.1.10"
    "@radix-ui/react-dialog": "npm:^1.1.14"
    "@radix-ui/react-dropdown-menu": "npm:^2.1.15"
    "@radix-ui/react-popover": "npm:^1.1.14"
    "@radix-ui/react-select": "npm:^2.2.5"
    "@radix-ui/react-slot": "npm:^1.2.3"
    "@radix-ui/react-switch": "npm:^1.2.5"
    "@radix-ui/react-tabs": "npm:^1.1.12"
    "@radix-ui/react-toggle": "npm:^1.1.9"
    "@radix-ui/react-toggle-group": "npm:^1.1.10"
    "@radix-ui/react-tooltip": "npm:^1.2.7"
    "@tailwindcss/vite": "npm:^4.0.15"
    "@types/express": "npm:^5.0.1"
    "@types/node": "npm:^22.15.21"
    "@types/react": "npm:^19.0.10"
    "@types/react-dom": "npm:^19.0.4"
    "@types/react-speech-recognition": "npm:^3.9.6"
    "@vitejs/plugin-react": "npm:^4.3.4"
    "@xenova/transformers": "npm:^2.17.2"
    body-parser: "npm:^1.20.3"
    class-variance-authority: "npm:^0.7.1"
    classnames: "npm:^2.5.1"
    clsx: "npm:^2.1.1"
    cookie-parser: "npm:^1.4.7"
    dotenv: "npm:^16.4.7"
    eslint: "npm:^9.21.0"
    eslint-plugin-react-hooks: "npm:^5.1.0"
    eslint-plugin-react-refresh: "npm:^0.4.19"
    express: "npm:^4.21.2"
    globals: "npm:^15.15.0"
    lucide-react: "npm:^0.511.0"
    monaco-editor: "npm:^0.52.2"
    next-themes: "npm:^0.4.6"
    openai: "npm:^5.1.1"
    react: "npm:^19.0.0"
    react-dom: "npm:^19.0.0"
    react-icons: "npm:^5.5.0"
    react-markdown: "npm:^10.1.0"
    react-speech-recognition: "npm:^4.0.0"
    react-toastify: "npm:^11.0.5"
    react-use: "npm:^17.6.0"
    sonner: "npm:^2.0.3"
    tailwind-merge: "npm:^3.3.0"
    tailwindcss: "npm:^4.0.15"
    tw-animate-css: "npm:^1.3.0"
    typescript: "npm:~5.7.2"
    typescript-eslint: "npm:^8.24.1"
    vite: "npm:^6.2.0"
    zod: "npm:^3.24.1"
  languageName: unknown
  linkType: soft

"html-url-attributes@npm:^3.0.0":
  version: 3.0.1
  resolution: "html-url-attributes@npm:3.0.1"
  checksum: 10c0/496e4908aa8b77665f348b4b03521901794f648b8ac34a581022cd6f2c97934d5c910cd91bc6593bbf2994687549037bc2520fcdc769b31484f29ffdd402acd0
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.2.0
  resolution: "http-cache-semantics@npm:4.2.0"
  checksum: 10c0/45b66a945cf13ec2d1f29432277201313babf4a01d9e52f44b31ca923434083afeca03f18417f599c9ab3d0e7b618ceb21257542338b57c54b710463b4a53e37
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: "npm:2.0.0"
    inherits: "npm:2.0.4"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    toidentifier: "npm:1.0.1"
  checksum: 10c0/fc6f2715fe188d091274b5ffc8b3657bd85c63e969daa68ccb77afb05b071a4b62841acb7a21e417b5539014dff2ebf9550f0b14a9ff126f2734a7c1387f8e19
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10c0/4207b06a4580fb85dd6dff521f0abf6db517489e70863dca1a0291daa7f2d3d2d6015a57bd702af068ea5cf9f1f6ff72314f5f5b4228d299c0904135d2aef921
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10c0/f729219bc735edb621fa30e6e84e60ee5d00802b8247aac0d7b79b0bd6d4b3294737a337b93b86a0bd9e68099d031858a39260c976dc14cdbba238ba1f8779ac
  languageName: node
  linkType: hard

"humanize-ms@npm:^1.2.1":
  version: 1.2.1
  resolution: "humanize-ms@npm:1.2.1"
  dependencies:
    ms: "npm:^2.0.0"
  checksum: 10c0/f34a2c20161d02303c2807badec2f3b49cbfbbb409abd4f95a07377ae01cfe6b59e3d15ac609cffcd8f2521f0eb37b7e1091acf65da99aa2a4f1ad63c21e7e7a
  languageName: node
  linkType: hard

"hyphenate-style-name@npm:^1.0.3":
  version: 1.1.0
  resolution: "hyphenate-style-name@npm:1.1.0"
  checksum: 10c0/bfe88deac2414a41a0d08811e277c8c098f23993d6a1eb17f14a0f11b54c4d42865a63d3cfe1914668eefb9a188e2de58f38b55a179a238fd1fef606893e194f
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3"
  checksum: 10c0/c6886a24cc00f2a059767440ec1bc00d334a89f250db8e0f7feb4961c8727118457e27c495ba94d082e51d3baca378726cd110aaf7ded8b9bbfd6a44760cf1d4
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10c0/98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.13":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 10c0/b0782ef5e0935b9f12883a2e2aa37baa75da6e66ce6515c168697b42160807d9330de9a32ec1ed73149aea02e0d822e572bca6f1e22bdcbd2149e13b050b17bb
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 10c0/f9f652c957983634ded1e7f02da3b559a0d4cc210fca3792cb67f1b153623c9c42efdc1c4121af171e295444459fc4a9201101fb041b1104a3c000bccb188337
  languageName: node
  linkType: hard

"ignore@npm:^7.0.0":
  version: 7.0.5
  resolution: "ignore@npm:7.0.5"
  checksum: 10c0/ae00db89fe873064a093b8999fe4cc284b13ef2a178636211842cceb650b9c3e390d3339191acb145d81ed5379d2074840cf0c33a20bdbd6f32821f79eb4ad5d
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1":
  version: 3.3.1
  resolution: "import-fresh@npm:3.3.1"
  dependencies:
    parent-module: "npm:^1.0.0"
    resolve-from: "npm:^4.0.0"
  checksum: 10c0/bf8cc494872fef783249709385ae883b447e3eb09db0ebd15dcead7d9afe7224dad7bd7591c6b73b0b19b3c0f9640eb8ee884f01cfaf2887ab995b0b36a0cbec
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10c0/8b51313850dd33605c6c9d3fd9638b714f4c4c40250cff658209f30d40da60f78992fb2df5dabee4acf589a6a82bbc79ad5486550754bd9ec4e3fc0d4a57d6a6
  languageName: node
  linkType: hard

"inherits@npm:2.0.4, inherits@npm:^2.0.3, inherits@npm:^2.0.4":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10c0/4e531f648b29039fb7426fb94075e6545faa1eb9fe83c29f0b6d9e7263aceb4289d2d4557db0d428188eeb449cc7c5e77b0a0b2c4e248ff2a65933a0dee49ef2
  languageName: node
  linkType: hard

"ini@npm:~1.3.0":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: 10c0/ec93838d2328b619532e4f1ff05df7909760b6f66d9c9e2ded11e5c1897d6f2f9980c54dd638f88654b00919ce31e827040631eab0a3969e4d1abefa0719516a
  languageName: node
  linkType: hard

"inline-style-parser@npm:0.2.4":
  version: 0.2.4
  resolution: "inline-style-parser@npm:0.2.4"
  checksum: 10c0/ddc0b210eaa03e0f98d677b9836242c583c7c6051e84ce0e704ae4626e7871c5b78f8e30853480218b446355745775df318d4f82d33087ff7e393245efa9a881
  languageName: node
  linkType: hard

"inline-style-prefixer@npm:^7.0.1":
  version: 7.0.1
  resolution: "inline-style-prefixer@npm:7.0.1"
  dependencies:
    css-in-js-utils: "npm:^3.1.0"
  checksum: 10c0/15da5a396b7f286b5b6742efe315218cd577bc96b43de08aeb76af7697d9f1ab3bfc66cf19fad2173957dd5d617a790240b9d51898bdcf4c2efb40d3f8bcb370
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10c0/331cd07fafcb3b24100613e4b53e1a2b4feab11e671e655d46dc09ee233da5011284d09ca40c4ecbdfe1d0004f462958675c224a804259f2f78d2465a87824bc
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: 10c0/0486e775047971d3fdb5fb4f063829bac45af299ae0b82dcf3afa2145338e08290563a2a70f34b732d795ecc8311902e541a8530eeb30d75860a78ff4e94ce2a
  languageName: node
  linkType: hard

"is-alphabetical@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-alphabetical@npm:2.0.1"
  checksum: 10c0/932367456f17237533fd1fc9fe179df77957271020b83ea31da50e5cc472d35ef6b5fb8147453274ffd251134472ce24eb6f8d8398d96dee98237cdb81a6c9a7
  languageName: node
  linkType: hard

"is-alphanumerical@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-alphanumerical@npm:2.0.1"
  dependencies:
    is-alphabetical: "npm:^2.0.0"
    is-decimal: "npm:^2.0.0"
  checksum: 10c0/4b35c42b18e40d41378293f82a3ecd9de77049b476f748db5697c297f686e1e05b072a6aaae2d16f54d2a57f85b00cbbe755c75f6d583d1c77d6657bd0feb5a2
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.3.1":
  version: 0.3.2
  resolution: "is-arrayish@npm:0.3.2"
  checksum: 10c0/f59b43dc1d129edb6f0e282595e56477f98c40278a2acdc8b0a5c57097c9eff8fe55470493df5775478cf32a4dc8eaf6d3a749f07ceee5bc263a78b2434f6a54
  languageName: node
  linkType: hard

"is-decimal@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-decimal@npm:2.0.1"
  checksum: 10c0/8085dd66f7d82f9de818fba48b9e9c0429cb4291824e6c5f2622e96b9680b54a07a624cfc663b24148b8e853c62a1c987cfe8b0b5a13f5156991afaf6736e334
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10c0/5487da35691fbc339700bbb2730430b07777a3c21b9ebaecb3072512dfd7b4ba78ac2381a87e8d78d20ea08affb3f1971b4af629173a6bf435ff8a4c47747912
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10c0/bb11d825e049f38e04c06373a8d72782eee0205bda9d908cc550ccb3c59b99d750ff9537982e01733c1c94a58e35400661f57042158ff5e8f3e90cf936daf0fc
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10c0/17fb4014e22be3bbecea9b2e3a76e9e34ff645466be702f1693e8f1ee1adac84710d0be0bd9f967d6354036fd51ab7c2741d954d6e91dae6bb69714de92c197a
  languageName: node
  linkType: hard

"is-hexadecimal@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-hexadecimal@npm:2.0.1"
  checksum: 10c0/3eb60fe2f1e2bbc760b927dcad4d51eaa0c60138cf7fc671803f66353ad90c301605b502c7ea4c6bb0548e1c7e79dfd37b73b632652e3b76030bba603a7e9626
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10c0/b4686d0d3053146095ccd45346461bc8e53b80aeb7671cc52a4de02dbbf7dc0d1d2a986e2fe4ae206984b4d34ef37e8b795ebc4f4295c978373e6575e295d811
  languageName: node
  linkType: hard

"is-plain-obj@npm:^4.0.0":
  version: 4.1.0
  resolution: "is-plain-obj@npm:4.1.0"
  checksum: 10c0/32130d651d71d9564dc88ba7e6fda0e91a1010a3694648e9f4f47bb6080438140696d3e3e15c741411d712e47ac9edc1a8a9de1fe76f3487b0d90be06ac9975e
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10c0/228cfa503fadc2c31596ab06ed6aa82c9976eec2bfd83397e7eaf06d0ccf42cd1dfd6743bf9aeb01aebd4156d009994c5f76ea898d2832c1fe342da923ca457d
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10c0/9ec257654093443eb0a528a9c8cbba9c0ca7616ccb40abd6dde7202734d96bb86e4ac0d764f0f8cd965856aacbff2f4ce23e730dc19dfb41e3b0d865ca6fdcc7
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10c0/6acc10d139eaefdbe04d2f679e6191b3abf073f111edf10b1de5302c97ec93fffeb2fdd8681ed17f16268aa9dd4f8c588ed9d1d3bffbbfa6e8bf897cbb3149b9
  languageName: node
  linkType: hard

"jiti@npm:^2.4.2":
  version: 2.4.2
  resolution: "jiti@npm:2.4.2"
  bin:
    jiti: lib/jiti-cli.mjs
  checksum: 10c0/4ceac133a08c8faff7eac84aabb917e85e8257f5ad659e843004ce76e981c457c390a220881748ac67ba1b940b9b729b30fb85cbaf6e7989f04b6002c94da331
  languageName: node
  linkType: hard

"js-cookie@npm:^2.2.1":
  version: 2.2.1
  resolution: "js-cookie@npm:2.2.1"
  checksum: 10c0/ee67fc0f8495d0800b851910b5eb5bf49d3033adff6493d55b5c097ca6da46f7fe666b10e2ecb13cfcaf5b88d71c205ce00a7e646de791689bfd053bbb36a376
  languageName: node
  linkType: hard

"js-tiktoken@npm:^1.0.12":
  version: 1.0.20
  resolution: "js-tiktoken@npm:1.0.20"
  dependencies:
    base64-js: "npm:^1.5.1"
  checksum: 10c0/846c9257b25efa153695bb2a4a85f35e2e747fa76f03d04dcaffee5b64ce22ccd61db83af099492558375eabf18c8070369d0f88de07a9fcf4cb494bb9759dbd
  languageName: node
  linkType: hard

"js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10c0/e248708d377aa058eacf2037b07ded847790e6de892bbad3dac0abba2e759cb9f121b00099a65195616badcb6eca8d14d975cb3e89eb1cfda644756402c8aeed
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/184a24b4eaacfce40ad9074c64fd42ac83cf74d8c8cd137718d456ced75051229e5061b8633c3366b8aada17945a7a356b337828c19da92b51ae62126575018f
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10c0/4f907fb78d7b712e11dea8c165fe0921f81a657d3443dde75359ed52eb2b5d33ce6773d97985a089f09a65edd80b11cb75c767b57ba47391fee4c969f7215c96
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 10c0/531779df5ec94f47e462da26b4cbf05eb88a83d9f08aac2ba04206508fc598527a153d08bd462bae82fc78b3eaa1a908e1a4a79f886e9238641c4cdefaf118b1
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 10c0/0d1c91569d9588e7eef2b49b59851f297f3ab93c7b35c7c221e288099322be6b562767d11e4821da500f3219542b9afd2e54c5dc573107c1126ed1080f8e96d7
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 10c0/108fa90d4cc6f08243aedc6da16c408daf81793bf903e9fd5ab21983cda433d5d2da49e40711da016289465ec2e62e0324dcdfbc06275a607fe3233fde4942ce
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: 10c0/cb168b61fd4de83e58d09aaa6425ef71001bae30d260e2c57e7d09a5fd82223e2f22a042dedaab8db23b7d9ae46854b08bb1f91675a8be11c5cffebef5fb66a5
  languageName: node
  linkType: hard

"json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 10c0/5a04eed94810fa55c5ea138b2f7a5c12b97c3750bc63d11e511dcecbfef758003861522a070c2272764ee0f4e3e323862f386945aeb5b85b87ee43f084ba586c
  languageName: node
  linkType: hard

"keyv@npm:^4.5.4":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: "npm:3.0.1"
  checksum: 10c0/aa52f3c5e18e16bb6324876bb8b59dd02acf782a4b789c7b2ae21107fab95fab3890ed448d4f8dba80ce05391eeac4bfabb4f02a20221342982f806fa2cf271e
  languageName: node
  linkType: hard

"langsmith@npm:^0.3.29":
  version: 0.3.30
  resolution: "langsmith@npm:0.3.30"
  dependencies:
    "@types/uuid": "npm:^10.0.0"
    chalk: "npm:^4.1.2"
    console-table-printer: "npm:^2.12.1"
    p-queue: "npm:^6.6.2"
    p-retry: "npm:4"
    semver: "npm:^7.6.3"
    uuid: "npm:^10.0.0"
  peerDependencies:
    openai: "*"
  peerDependenciesMeta:
    openai:
      optional: true
  checksum: 10c0/6090206792cbe33a46dfbd91697dcb79763854f8fe332390bca127110d0133b0351068a08310eda258965faa01c9591aa35e3673dded9c67d235c04373ce6681
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:~0.4.0"
  checksum: 10c0/effb03cad7c89dfa5bd4f6989364bfc79994c2042ec5966cb9b95990e2edee5cd8969ddf42616a0373ac49fac1403437deaf6e9050fbbaa3546093a59b9ac94e
  languageName: node
  linkType: hard

"lightningcss-darwin-arm64@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-darwin-arm64@npm:1.30.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"lightningcss-darwin-x64@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-darwin-x64@npm:1.30.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"lightningcss-freebsd-x64@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-freebsd-x64@npm:1.30.1"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"lightningcss-linux-arm-gnueabihf@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-linux-arm-gnueabihf@npm:1.30.1"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"lightningcss-linux-arm64-gnu@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-linux-arm64-gnu@npm:1.30.1"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"lightningcss-linux-arm64-musl@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-linux-arm64-musl@npm:1.30.1"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"lightningcss-linux-x64-gnu@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-linux-x64-gnu@npm:1.30.1"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"lightningcss-linux-x64-musl@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-linux-x64-musl@npm:1.30.1"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"lightningcss-win32-arm64-msvc@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-win32-arm64-msvc@npm:1.30.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"lightningcss-win32-x64-msvc@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-win32-x64-msvc@npm:1.30.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"lightningcss@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss@npm:1.30.1"
  dependencies:
    detect-libc: "npm:^2.0.3"
    lightningcss-darwin-arm64: "npm:1.30.1"
    lightningcss-darwin-x64: "npm:1.30.1"
    lightningcss-freebsd-x64: "npm:1.30.1"
    lightningcss-linux-arm-gnueabihf: "npm:1.30.1"
    lightningcss-linux-arm64-gnu: "npm:1.30.1"
    lightningcss-linux-arm64-musl: "npm:1.30.1"
    lightningcss-linux-x64-gnu: "npm:1.30.1"
    lightningcss-linux-x64-musl: "npm:1.30.1"
    lightningcss-win32-arm64-msvc: "npm:1.30.1"
    lightningcss-win32-x64-msvc: "npm:1.30.1"
  dependenciesMeta:
    lightningcss-darwin-arm64:
      optional: true
    lightningcss-darwin-x64:
      optional: true
    lightningcss-freebsd-x64:
      optional: true
    lightningcss-linux-arm-gnueabihf:
      optional: true
    lightningcss-linux-arm64-gnu:
      optional: true
    lightningcss-linux-arm64-musl:
      optional: true
    lightningcss-linux-x64-gnu:
      optional: true
    lightningcss-linux-x64-musl:
      optional: true
    lightningcss-win32-arm64-msvc:
      optional: true
    lightningcss-win32-x64-msvc:
      optional: true
  checksum: 10c0/1e1ad908f3c68bf39d964a6735435a8dd5474fb2765076732d64a7b6aa2af1f084da65a9462443a9adfebf7dcfb02fb532fce1d78697f2a9de29c8f40f09aee3
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: "npm:^5.0.0"
  checksum: 10c0/d3972ab70dfe58ce620e64265f90162d247e87159b6126b01314dd67be43d50e96a50b517bce2d9452a79409c7614054c277b5232377de50416564a77ac7aad3
  languageName: node
  linkType: hard

"lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: 10c0/762998a63e095412b6099b8290903e0a8ddcb353ac6e2e0f2d7e7d03abd4275fe3c689d88960eb90b0dde4f177554d51a690f22a343932ecbc50a5d111849987
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: 10c0/402fa16a1edd7538de5b5903a90228aa48eb5533986ba7fa26606a49db2572bf414ff73a2c9f5d5fd36b31c46a5d5c7e1527749c07cbcf965ccff5fbdf32c506
  languageName: node
  linkType: hard

"long@npm:^4.0.0":
  version: 4.0.0
  resolution: "long@npm:4.0.0"
  checksum: 10c0/50a6417d15b06104dbe4e3d4a667c39b137f130a9108ea8752b352a4cfae047531a3ac351c181792f3f8768fe17cca6b0f406674a541a86fb638aaac560d83ed
  languageName: node
  linkType: hard

"longest-streak@npm:^3.0.0":
  version: 3.1.0
  resolution: "longest-streak@npm:3.1.0"
  checksum: 10c0/7c2f02d0454b52834d1bcedef79c557bd295ee71fdabb02d041ff3aa9da48a90b5df7c0409156dedbc4df9b65da18742652aaea4759d6ece01f08971af6a7eaa
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10c0/ebd04fbca961e6c1d6c0af3799adcc966a1babe798f685bb84e6599266599cd95d94630b10262f5424539bc4640107e8a33aa28585374abf561d30d16f4b39fb
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: "npm:^3.0.2"
  checksum: 10c0/89b2ef2ef45f543011e38737b8a8622a2f8998cddf0e5437174ef8f1f70a8b9d14a918ab3e232cb3ba343b7abddffa667f0b59075b2b80e6b4d63c3de6127482
  languageName: node
  linkType: hard

"lucide-react@npm:^0.511.0":
  version: 0.511.0
  resolution: "lucide-react@npm:0.511.0"
  peerDependencies:
    react: ^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10c0/bf09dd73cf2233abea90506ad31a91739555d761062722acbe045cb73e274f035b196472de0971a8a8f0645b2b54e3f21b8c1980fe87c909ca93171a9c28428a
  languageName: node
  linkType: hard

"magic-string@npm:^0.30.17":
  version: 0.30.17
  resolution: "magic-string@npm:0.30.17"
  dependencies:
    "@jridgewell/sourcemap-codec": "npm:^1.5.0"
  checksum: 10c0/16826e415d04b88378f200fe022b53e638e3838b9e496edda6c0e086d7753a44a6ed187adc72d19f3623810589bf139af1a315541cd6a26ae0771a0193eaf7b8
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10c0/c40efb5e5296e7feb8e37155bde8eb70bc57d731b1f7d90e35a092fde403d7697c56fb49334d92d330d6f1ca29a98142036d6480a12681133a0a1453164cb2f0
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10c0/7579ff94e899e2f76ab64491d76cf606274c874d8f2af4a442c016bd85688927fcfca157ba6bf74b08e9439dc010b248ce05b96cc7c126a354c3bae7fcb48b7f
  languageName: node
  linkType: hard

"mdast-util-from-markdown@npm:^2.0.0":
  version: 2.0.2
  resolution: "mdast-util-from-markdown@npm:2.0.2"
  dependencies:
    "@types/mdast": "npm:^4.0.0"
    "@types/unist": "npm:^3.0.0"
    decode-named-character-reference: "npm:^1.0.0"
    devlop: "npm:^1.0.0"
    mdast-util-to-string: "npm:^4.0.0"
    micromark: "npm:^4.0.0"
    micromark-util-decode-numeric-character-reference: "npm:^2.0.0"
    micromark-util-decode-string: "npm:^2.0.0"
    micromark-util-normalize-identifier: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
    unist-util-stringify-position: "npm:^4.0.0"
  checksum: 10c0/76eb2bd2c6f7a0318087c73376b8af6d7561c1e16654e7667e640f391341096c56142618fd0ff62f6d39e5ab4895898b9789c84cd7cec2874359a437a0e1ff15
  languageName: node
  linkType: hard

"mdast-util-mdx-expression@npm:^2.0.0":
  version: 2.0.1
  resolution: "mdast-util-mdx-expression@npm:2.0.1"
  dependencies:
    "@types/estree-jsx": "npm:^1.0.0"
    "@types/hast": "npm:^3.0.0"
    "@types/mdast": "npm:^4.0.0"
    devlop: "npm:^1.0.0"
    mdast-util-from-markdown: "npm:^2.0.0"
    mdast-util-to-markdown: "npm:^2.0.0"
  checksum: 10c0/9a1e57940f66431f10312fa239096efa7627f375e7933b5d3162c0b5c1712a72ac87447aff2b6838d2bbd5c1311b188718cc90b33b67dc67a88550e0a6ef6183
  languageName: node
  linkType: hard

"mdast-util-mdx-jsx@npm:^3.0.0":
  version: 3.2.0
  resolution: "mdast-util-mdx-jsx@npm:3.2.0"
  dependencies:
    "@types/estree-jsx": "npm:^1.0.0"
    "@types/hast": "npm:^3.0.0"
    "@types/mdast": "npm:^4.0.0"
    "@types/unist": "npm:^3.0.0"
    ccount: "npm:^2.0.0"
    devlop: "npm:^1.1.0"
    mdast-util-from-markdown: "npm:^2.0.0"
    mdast-util-to-markdown: "npm:^2.0.0"
    parse-entities: "npm:^4.0.0"
    stringify-entities: "npm:^4.0.0"
    unist-util-stringify-position: "npm:^4.0.0"
    vfile-message: "npm:^4.0.0"
  checksum: 10c0/3acadaf3b962254f7ad2990fed4729961dc0217ca31fde9917986e880843f3ecf3392b1f22d569235cacd180d50894ad266db7af598aedca69d330d33c7ac613
  languageName: node
  linkType: hard

"mdast-util-mdxjs-esm@npm:^2.0.0":
  version: 2.0.1
  resolution: "mdast-util-mdxjs-esm@npm:2.0.1"
  dependencies:
    "@types/estree-jsx": "npm:^1.0.0"
    "@types/hast": "npm:^3.0.0"
    "@types/mdast": "npm:^4.0.0"
    devlop: "npm:^1.0.0"
    mdast-util-from-markdown: "npm:^2.0.0"
    mdast-util-to-markdown: "npm:^2.0.0"
  checksum: 10c0/5bda92fc154141705af2b804a534d891f28dac6273186edf1a4c5e3f045d5b01dbcac7400d27aaf91b7e76e8dce007c7b2fdf136c11ea78206ad00bdf9db46bc
  languageName: node
  linkType: hard

"mdast-util-phrasing@npm:^4.0.0":
  version: 4.1.0
  resolution: "mdast-util-phrasing@npm:4.1.0"
  dependencies:
    "@types/mdast": "npm:^4.0.0"
    unist-util-is: "npm:^6.0.0"
  checksum: 10c0/bf6c31d51349aa3d74603d5e5a312f59f3f65662ed16c58017169a5fb0f84ca98578f626c5ee9e4aa3e0a81c996db8717096705521bddb4a0185f98c12c9b42f
  languageName: node
  linkType: hard

"mdast-util-to-hast@npm:^13.0.0":
  version: 13.2.0
  resolution: "mdast-util-to-hast@npm:13.2.0"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    "@types/mdast": "npm:^4.0.0"
    "@ungap/structured-clone": "npm:^1.0.0"
    devlop: "npm:^1.0.0"
    micromark-util-sanitize-uri: "npm:^2.0.0"
    trim-lines: "npm:^3.0.0"
    unist-util-position: "npm:^5.0.0"
    unist-util-visit: "npm:^5.0.0"
    vfile: "npm:^6.0.0"
  checksum: 10c0/9ee58def9287df8350cbb6f83ced90f9c088d72d4153780ad37854f87144cadc6f27b20347073b285173b1649b0723ddf0b9c78158608a804dcacb6bda6e1816
  languageName: node
  linkType: hard

"mdast-util-to-markdown@npm:^2.0.0":
  version: 2.1.2
  resolution: "mdast-util-to-markdown@npm:2.1.2"
  dependencies:
    "@types/mdast": "npm:^4.0.0"
    "@types/unist": "npm:^3.0.0"
    longest-streak: "npm:^3.0.0"
    mdast-util-phrasing: "npm:^4.0.0"
    mdast-util-to-string: "npm:^4.0.0"
    micromark-util-classify-character: "npm:^2.0.0"
    micromark-util-decode-string: "npm:^2.0.0"
    unist-util-visit: "npm:^5.0.0"
    zwitch: "npm:^2.0.0"
  checksum: 10c0/4649722a6099f12e797bd8d6469b2b43b44e526b5182862d9c7766a3431caad2c0112929c538a972f214e63c015395e5d3f54bd81d9ac1b16e6d8baaf582f749
  languageName: node
  linkType: hard

"mdast-util-to-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "mdast-util-to-string@npm:4.0.0"
  dependencies:
    "@types/mdast": "npm:^4.0.0"
  checksum: 10c0/2d3c1af29bf3fe9c20f552ee9685af308002488f3b04b12fa66652c9718f66f41a32f8362aa2d770c3ff464c034860b41715902ada2306bb0a055146cef064d7
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.14":
  version: 2.0.14
  resolution: "mdn-data@npm:2.0.14"
  checksum: 10c0/67241f8708c1e665a061d2b042d2d243366e93e5bf1f917693007f6d55111588b952dcbfd3ea9c2d0969fb754aad81b30fdcfdcc24546495fc3b24336b28d4bd
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: 10c0/d160f31246907e79fed398470285f21bafb45a62869dc469b1c8877f3f064f5eabc4bcc122f9479b8b605bc5c76187d7871cf84c4ee3ecd3e487da1993279928
  languageName: node
  linkType: hard

"merge-descriptors@npm:1.0.3":
  version: 1.0.3
  resolution: "merge-descriptors@npm:1.0.3"
  checksum: 10c0/866b7094afd9293b5ea5dcd82d71f80e51514bed33b4c4e9f516795dc366612a4cbb4dc94356e943a8a6914889a914530badff27f397191b9b75cda20b6bae93
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10c0/254a8a4605b58f450308fc474c82ac9a094848081bf4c06778200207820e5193726dc563a0d2c16468810516a5c97d9d3ea0ca6585d23c58ccfff2403e8dbbeb
  languageName: node
  linkType: hard

"methods@npm:~1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: 10c0/bdf7cc72ff0a33e3eede03708c08983c4d7a173f91348b4b1e4f47d4cdbf734433ad971e7d1e8c77247d9e5cd8adb81ea4c67b0a2db526b758b2233d7814b8b2
  languageName: node
  linkType: hard

"micromark-core-commonmark@npm:^2.0.0":
  version: 2.0.3
  resolution: "micromark-core-commonmark@npm:2.0.3"
  dependencies:
    decode-named-character-reference: "npm:^1.0.0"
    devlop: "npm:^1.0.0"
    micromark-factory-destination: "npm:^2.0.0"
    micromark-factory-label: "npm:^2.0.0"
    micromark-factory-space: "npm:^2.0.0"
    micromark-factory-title: "npm:^2.0.0"
    micromark-factory-whitespace: "npm:^2.0.0"
    micromark-util-character: "npm:^2.0.0"
    micromark-util-chunked: "npm:^2.0.0"
    micromark-util-classify-character: "npm:^2.0.0"
    micromark-util-html-tag-name: "npm:^2.0.0"
    micromark-util-normalize-identifier: "npm:^2.0.0"
    micromark-util-resolve-all: "npm:^2.0.0"
    micromark-util-subtokenize: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10c0/bd4a794fdc9e88dbdf59eaf1c507ddf26e5f7ddf4e52566c72239c0f1b66adbcd219ba2cd42350debbe24471434d5f5e50099d2b3f4e5762ca222ba8e5b549ee
  languageName: node
  linkType: hard

"micromark-factory-destination@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-factory-destination@npm:2.0.1"
  dependencies:
    micromark-util-character: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10c0/bbafcf869cee5bf511161354cb87d61c142592fbecea051000ff116068dc85216e6d48519d147890b9ea5d7e2864a6341c0c09d9948c203bff624a80a476023c
  languageName: node
  linkType: hard

"micromark-factory-label@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-factory-label@npm:2.0.1"
  dependencies:
    devlop: "npm:^1.0.0"
    micromark-util-character: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10c0/0137716b4ecb428114165505e94a2f18855c8bbea21b07a8b5ce514b32a595ed789d2b967125718fc44c4197ceaa48f6609d58807a68e778138d2e6b91b824e8
  languageName: node
  linkType: hard

"micromark-factory-space@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-factory-space@npm:2.0.1"
  dependencies:
    micromark-util-character: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10c0/f9ed43f1c0652d8d898de0ac2be3f77f776fffe7dd96bdbba1e02d7ce33d3853c6ff5daa52568fc4fa32cdf3a62d86b85ead9b9189f7211e1d69ff2163c450fb
  languageName: node
  linkType: hard

"micromark-factory-title@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-factory-title@npm:2.0.1"
  dependencies:
    micromark-factory-space: "npm:^2.0.0"
    micromark-util-character: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10c0/e72fad8d6e88823514916890099a5af20b6a9178ccf78e7e5e05f4de99bb8797acb756257d7a3a57a53854cb0086bf8aab15b1a9e9db8982500dd2c9ff5948b6
  languageName: node
  linkType: hard

"micromark-factory-whitespace@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-factory-whitespace@npm:2.0.1"
  dependencies:
    micromark-factory-space: "npm:^2.0.0"
    micromark-util-character: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10c0/20a1ec58698f24b766510a309b23a10175034fcf1551eaa9da3adcbed3e00cd53d1ebe5f030cf873f76a1cec3c34eb8c50cc227be3344caa9ed25d56cf611224
  languageName: node
  linkType: hard

"micromark-util-character@npm:^2.0.0":
  version: 2.1.1
  resolution: "micromark-util-character@npm:2.1.1"
  dependencies:
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10c0/d3fe7a5e2c4060fc2a076f9ce699c82a2e87190a3946e1e5eea77f563869b504961f5668d9c9c014724db28ac32fa909070ea8b30c3a39bd0483cc6c04cc76a1
  languageName: node
  linkType: hard

"micromark-util-chunked@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-chunked@npm:2.0.1"
  dependencies:
    micromark-util-symbol: "npm:^2.0.0"
  checksum: 10c0/b68c0c16fe8106949537bdcfe1be9cf36c0ccd3bc54c4007003cb0984c3750b6cdd0fd77d03f269a3382b85b0de58bde4f6eedbe7ecdf7244759112289b1ab56
  languageName: node
  linkType: hard

"micromark-util-classify-character@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-classify-character@npm:2.0.1"
  dependencies:
    micromark-util-character: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10c0/8a02e59304005c475c332f581697e92e8c585bcd45d5d225a66c1c1b14ab5a8062705188c2ccec33cc998d33502514121478b2091feddbc751887fc9c290ed08
  languageName: node
  linkType: hard

"micromark-util-combine-extensions@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-combine-extensions@npm:2.0.1"
  dependencies:
    micromark-util-chunked: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10c0/f15e282af24c8372cbb10b9b0b3e2c0aa681fea0ca323a44d6bc537dc1d9382c819c3689f14eaa000118f5a163245358ce6276b2cda9a84439cdb221f5d86ae7
  languageName: node
  linkType: hard

"micromark-util-decode-numeric-character-reference@npm:^2.0.0":
  version: 2.0.2
  resolution: "micromark-util-decode-numeric-character-reference@npm:2.0.2"
  dependencies:
    micromark-util-symbol: "npm:^2.0.0"
  checksum: 10c0/9c8a9f2c790e5593ffe513901c3a110e9ec8882a08f466da014112a25e5059b51551ca0aeb7ff494657d86eceb2f02ee556c6558b8d66aadc61eae4a240da0df
  languageName: node
  linkType: hard

"micromark-util-decode-string@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-decode-string@npm:2.0.1"
  dependencies:
    decode-named-character-reference: "npm:^1.0.0"
    micromark-util-character: "npm:^2.0.0"
    micromark-util-decode-numeric-character-reference: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
  checksum: 10c0/f24d75b2e5310be6e7b6dee532e0d17d3bf46996841d6295f2a9c87a2046fff4ab603c52ab9d7a7a6430a8b787b1574ae895849c603d262d1b22eef71736b5cb
  languageName: node
  linkType: hard

"micromark-util-encode@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-encode@npm:2.0.1"
  checksum: 10c0/b2b29f901093845da8a1bf997ea8b7f5e061ffdba85070dfe14b0197c48fda64ffcf82bfe53c90cf9dc185e69eef8c5d41cae3ba918b96bc279326921b59008a
  languageName: node
  linkType: hard

"micromark-util-html-tag-name@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-html-tag-name@npm:2.0.1"
  checksum: 10c0/ae80444db786fde908e9295f19a27a4aa304171852c77414516418650097b8afb401961c9edb09d677b06e97e8370cfa65638dde8438ebd41d60c0a8678b85b9
  languageName: node
  linkType: hard

"micromark-util-normalize-identifier@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-normalize-identifier@npm:2.0.1"
  dependencies:
    micromark-util-symbol: "npm:^2.0.0"
  checksum: 10c0/5299265fa360769fc499a89f40142f10a9d4a5c3dd8e6eac8a8ef3c2e4a6570e4c009cf75ea46dce5ee31c01f25587bde2f4a5cc0a935584ae86dd857f2babbd
  languageName: node
  linkType: hard

"micromark-util-resolve-all@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-resolve-all@npm:2.0.1"
  dependencies:
    micromark-util-types: "npm:^2.0.0"
  checksum: 10c0/bb6ca28764696bb479dc44a2d5b5fe003e7177aeae1d6b0d43f24cc223bab90234092d9c3ce4a4d2b8df095ccfd820537b10eb96bb7044d635f385d65a4c984a
  languageName: node
  linkType: hard

"micromark-util-sanitize-uri@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-sanitize-uri@npm:2.0.1"
  dependencies:
    micromark-util-character: "npm:^2.0.0"
    micromark-util-encode: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
  checksum: 10c0/60e92166e1870fd4f1961468c2651013ff760617342918e0e0c3c4e872433aa2e60c1e5a672bfe5d89dc98f742d6b33897585cf86ae002cda23e905a3c02527c
  languageName: node
  linkType: hard

"micromark-util-subtokenize@npm:^2.0.0":
  version: 2.1.0
  resolution: "micromark-util-subtokenize@npm:2.1.0"
  dependencies:
    devlop: "npm:^1.0.0"
    micromark-util-chunked: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10c0/bee69eece4393308e657c293ba80d92ebcb637e5f55e21dcf9c3fa732b91a8eda8ac248d76ff375e675175bfadeae4712e5158ef97eef1111789da1ce7ab5067
  languageName: node
  linkType: hard

"micromark-util-symbol@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-symbol@npm:2.0.1"
  checksum: 10c0/f2d1b207771e573232436618e78c5e46cd4b5c560dd4a6d63863d58018abbf49cb96ec69f7007471e51434c60de3c9268ef2bf46852f26ff4aacd10f9da16fe9
  languageName: node
  linkType: hard

"micromark-util-types@npm:^2.0.0":
  version: 2.0.2
  resolution: "micromark-util-types@npm:2.0.2"
  checksum: 10c0/c8c15b96c858db781c4393f55feec10004bf7df95487636c9a9f7209e51002a5cca6a047c5d2a5dc669ff92da20e57aaa881e81a268d9ccadb647f9dce305298
  languageName: node
  linkType: hard

"micromark@npm:^4.0.0":
  version: 4.0.2
  resolution: "micromark@npm:4.0.2"
  dependencies:
    "@types/debug": "npm:^4.0.0"
    debug: "npm:^4.0.0"
    decode-named-character-reference: "npm:^1.0.0"
    devlop: "npm:^1.0.0"
    micromark-core-commonmark: "npm:^2.0.0"
    micromark-factory-space: "npm:^2.0.0"
    micromark-util-character: "npm:^2.0.0"
    micromark-util-chunked: "npm:^2.0.0"
    micromark-util-combine-extensions: "npm:^2.0.0"
    micromark-util-decode-numeric-character-reference: "npm:^2.0.0"
    micromark-util-encode: "npm:^2.0.0"
    micromark-util-normalize-identifier: "npm:^2.0.0"
    micromark-util-resolve-all: "npm:^2.0.0"
    micromark-util-sanitize-uri: "npm:^2.0.0"
    micromark-util-subtokenize: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10c0/07462287254219d6eda6eac8a3cebaff2994e0575499e7088027b825105e096e4f51e466b14b2a81b71933a3b6c48ee069049d87bc2c2127eee50d9cc69e8af6
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: "npm:^3.0.3"
    picomatch: "npm:^2.3.1"
  checksum: 10c0/166fa6eb926b9553f32ef81f5f531d27b4ce7da60e5baf8c021d043b27a388fb95e46a8038d5045877881e673f8134122b59624d5cecbd16eb50a42e7a6b5ca8
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10c0/0557a01deebf45ac5f5777fe7740b2a5c309c6d62d40ceab4e23da9f821899ce7a900b7ac8157d4548ddbb7beffe9abc621250e6d182b0397ec7f10c7b91a5aa
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:~2.1.24, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10c0/82fb07ec56d8ff1fc999a84f2f217aa46cb6ed1033fefaabd5785b9a974ed225c90dc72fff460259e66b95b73648596dbcc50d51ed69cdf464af2d237d3149b2
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: 10c0/b92cd0adc44888c7135a185bfd0dddc42c32606401c72896a842ae15da71eb88858f17669af41e498b463cd7eb998f7b48939a25b08374c7924a9c8a6f8a81b0
  languageName: node
  linkType: hard

"mimic-response@npm:^3.1.0":
  version: 3.1.0
  resolution: "mimic-response@npm:3.1.0"
  checksum: 10c0/0d6f07ce6e03e9e4445bee655202153bdb8a98d67ee8dc965ac140900d7a2688343e6b4c9a72cfc9ef2f7944dfd76eef4ab2482eb7b293a68b84916bac735362
  languageName: node
  linkType: hard

"minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10c0/0262810a8fc2e72cca45d6fd86bd349eee435eb95ac6aa45c9ea2180e7ee875ef44c32b55b5973ceabe95ea12682f6e3725cbb63d7a2d1da3ae1163c8b210311
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/de96cf5e35bdf0eab3e2c853522f98ffbe9a36c37797778d2665231ec1f20a9447a7e567cb640901f89e4daaa95ae5d70c65a9e8aa2bb0019b6facbc3c0575ed
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.3":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 10c0/19d3fcdca050087b84c2029841a093691a91259a47def2f18222f41e7645a0b7c44ef4b40e88a1e58a40c84d2ef0ee6047c55594d298146d0eb3f6b737c20ce6
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/5167e73f62bb74cc5019594709c77e6a742051a647fe9499abf03c71dca75515b7959d67a764bdc4f8b361cf897fbf25e2d9869ee039203ed45240f48b9aa06e
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/a3147b2efe8e078c9bf9d024a0059339c5a09c5b1dded6900a219c218cc8b1b78510b62dae556b507304af226b18c3f1aeb1d48660283602d5b6586c399eed5c
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/2a51b63feb799d2bb34669205eee7c0eaf9dce01883261a5b77410c9408aa447e478efd191b4de6fc1101e796ff5892f8443ef20d9544385819093dbb32d36bd
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/cbda57cea20b140b797505dc2cac71581a70b3247b84480c1fed5ca5ba46c25ecc25f68bfc9e6dcb1a6e9017dab5c7ada5eab73ad4f0a49d84e35093e0c643f2
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/298f124753efdc745cfe0f2bdfdd81ba25b9f4e753ca4a2066eb17c821f25d48acea607dfc997633ee5bf7b6dfffb4eee4f2051eb168663f0b99fad2fa4829cb
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/a114746943afa1dbbca8249e706d1d38b85ed1298b530f5808ce51f8e9e941962e2a5ad2e00eae7dd21d8a4aae6586a66d4216d1a259385e9d0358f0c1eba16c
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10c0/b0fd20bb9fb56e5fa9a8bfac539e8915ae07430a619e4b86ff71f5fc757ef3924b23b2c4230393af1eda647ed3d75739e4e0acb250a6b1eb277cf7f8fe449557
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: "npm:^7.1.2"
  checksum: 10c0/9f3bd35e41d40d02469cb30470c55ccc21cae0db40e08d1d0b1dff01cc8cc89a6f78e9c5d2b7c844e485ec0a8abc2238111213fdc5b2038e6d1012eacf316f78
  languageName: node
  linkType: hard

"mkdirp-classic@npm:^0.5.2, mkdirp-classic@npm:^0.5.3":
  version: 0.5.3
  resolution: "mkdirp-classic@npm:0.5.3"
  checksum: 10c0/95371d831d196960ddc3833cc6907e6b8f67ac5501a6582f47dfae5eb0f092e9f8ce88e0d83afcae95d6e2b61a01741ba03714eeafb6f7a6e9dcc158ac85b168
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10c0/9f2b975e9246351f5e3a40dcfac99fcd0baa31fbfab615fe059fb11e51f10e4803c63de1f384c54d656e4db31d000e4767e9ef076a22e12a641357602e31d57d
  languageName: node
  linkType: hard

"monaco-editor@npm:^0.52.2":
  version: 0.52.2
  resolution: "monaco-editor@npm:0.52.2"
  checksum: 10c0/5a92da64f1e2ab375c0ce99364137f794d057c97bed10ecc65a08d6e6846804b8ecbd377eacf01e498f7dfbe1b21e8be64f728256681448f0484df90e767b435
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 10c0/f8fda810b39fd7255bbdc451c46286e549794fcc700dc9cd1d25658bbc4dc2563a5de6fe7c60f798a16a60c6ceb53f033cb353f493f0cf63e5199b702943159d
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.0.0, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10c0/d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"mustache@npm:^4.2.0":
  version: 4.2.0
  resolution: "mustache@npm:4.2.0"
  bin:
    mustache: bin/mustache
  checksum: 10c0/1f8197e8a19e63645a786581d58c41df7853da26702dbc005193e2437c98ca49b255345c173d50c08fe4b4dbb363e53cb655ecc570791f8deb09887248dd34a2
  languageName: node
  linkType: hard

"nano-css@npm:^5.6.2":
  version: 5.6.2
  resolution: "nano-css@npm:5.6.2"
  dependencies:
    "@jridgewell/sourcemap-codec": "npm:^1.4.15"
    css-tree: "npm:^1.1.2"
    csstype: "npm:^3.1.2"
    fastest-stable-stringify: "npm:^2.0.2"
    inline-style-prefixer: "npm:^7.0.1"
    rtl-css-js: "npm:^1.16.1"
    stacktrace-js: "npm:^2.0.2"
    stylis: "npm:^4.3.0"
  peerDependencies:
    react: "*"
    react-dom: "*"
  checksum: 10c0/566fb9403815d78a110d68f011e1125cbeeb7299e2e6c60700f316ba0c48dc702c039163eae7a8f213a1390e45cedfdeccc203794d61a61116598adbb83029ec
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.11":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10c0/40e7f70b3d15f725ca072dfc4f74e81fcf1fbb02e491cf58ac0c79093adc9b0a73b152bcde57df4b79cd097e13023d7504acb38404a4da7bc1cd8e887b82fe0b
  languageName: node
  linkType: hard

"napi-build-utils@npm:^2.0.0":
  version: 2.0.0
  resolution: "napi-build-utils@npm:2.0.0"
  checksum: 10c0/5833aaeb5cc5c173da47a102efa4680a95842c13e0d9cc70428bd3ee8d96bb2172f8860d2811799b5daa5cbeda779933601492a2028a6a5351c6d0fcf6de83db
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 10c0/f5f9a7974bfb28a91afafa254b197f0f22c684d4a1731763dda960d2c8e375b36c7d690e0d9dc8fba774c537af14a7e979129bca23d88d052fbeb9466955e447
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: 10c0/3ec9fd413e7bf071c937ae60d572bc67155262068ed522cf4b3be5edbe6ddf67d095ec03a3a14ebf8fc8e95f8e1d61be4869db0dbb0de696f6b837358bd43fc2
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10c0/4c559dd52669ea48e1914f9d634227c561221dd54734070791f999c52ed0ff36e437b2e07d5c1f6e32909fc625fe46491c16e4a8f0572567d4dd15c3a4fda04b
  languageName: node
  linkType: hard

"next-themes@npm:^0.4.6":
  version: 0.4.6
  resolution: "next-themes@npm:0.4.6"
  peerDependencies:
    react: ^16.8 || ^17 || ^18 || ^19 || ^19.0.0-rc
    react-dom: ^16.8 || ^17 || ^18 || ^19 || ^19.0.0-rc
  checksum: 10c0/83590c11d359ce7e4ced14f6ea9dd7a691d5ce6843fe2dc520fc27e29ae1c535118478d03e7f172609c41b1ef1b8da6b8dd2d2acd6cd79cac1abbdbd5b99f2c4
  languageName: node
  linkType: hard

"node-abi@npm:^3.3.0":
  version: 3.75.0
  resolution: "node-abi@npm:3.75.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10c0/c43a2409407df3737848fd96202b0a49e15039994aecce963969e9ef7342a8fc544aba94e0bfd8155fb9de5f5fe9a4b6ccad8bf509e7c46caf096fc4491d63f2
  languageName: node
  linkType: hard

"node-addon-api@npm:^6.1.0":
  version: 6.1.0
  resolution: "node-addon-api@npm:6.1.0"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/d2699c4ad15740fd31482a3b6fca789af7723ab9d393adc6ac45250faaee72edad8f0b10b2b9d087df0de93f1bdc16d97afdd179b26b9ebc9ed68b569faa4bac
  languageName: node
  linkType: hard

"node-domexception@npm:1.0.0":
  version: 1.0.0
  resolution: "node-domexception@npm:1.0.0"
  checksum: 10c0/5e5d63cda29856402df9472335af4bb13875e1927ad3be861dc5ebde38917aecbf9ae337923777af52a48c426b70148815e890a5d72760f1b4d758cc671b1a2b
  languageName: node
  linkType: hard

"node-fetch@npm:^2.6.7":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: "npm:^5.0.0"
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/b55786b6028208e6fbe594ccccc213cab67a72899c9234eb59dba51062a299ea853210fcf526998eaa2867b0963ad72338824450905679ff0fa304b8c5093ae8
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.2.0
  resolution: "node-gyp@npm:11.2.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    tinyglobby: "npm:^0.2.12"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10c0/bd8d8c76b06be761239b0c8680f655f6a6e90b48e44d43415b11c16f7e8c15be346fba0cbf71588c7cdfb52c419d928a7d3db353afc1d952d19756237d8f10b9
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 10c0/52a0dbd25ccf545892670d1551690fe0facb6a471e15f2cfa1b20142a5b255b3aa254af5f59d6ecb69c2bec7390bc643c43aa63b13bf5e64b6075952e716b1aa
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10c0/62e9ea70c7a3eb91d162d2c706b6606c041e4e7b547cbbb48f8b3695af457dd6479904d7ace600856bf923dd8d1ed0696f06195c8c20f02ac87c1da0e1d315ef
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 10c0/d7f8711e803b96ea3191c745d6f8056ce1f2496e530e6a19a0e92d89b0fa3c76d910c31f0aa270432db6bd3b2f85500a376a83aaba849a8d518c8845b3211692
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 10c0/46fb11b9063782f2d9968863d9cbba33d77aa13c17f895f56129c274318b86500b22af3a160fe9995aa41317efcd22941b6eba747f718ced08d9a73afdb087b4
  languageName: node
  linkType: hard

"once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: 10c0/5d48aca287dfefabd756621c5dfce5c91a549a93e9fdb7b8246bc4c4790aa2ec17b34a260530474635147aeb631a2dcc8b32c613df0675f96041cbb8244517d0
  languageName: node
  linkType: hard

"onnx-proto@npm:^4.0.4":
  version: 4.0.4
  resolution: "onnx-proto@npm:4.0.4"
  dependencies:
    protobufjs: "npm:^6.8.8"
  checksum: 10c0/f05200d7c4b322f538af2eacf19fd9942705090b69946d40152274fdcbcae4d423eb69a67cae875ba3630fa6f10949a62b37311659db7e77c7e1e68a089095af
  languageName: node
  linkType: hard

"onnxruntime-common@npm:~1.14.0":
  version: 1.14.0
  resolution: "onnxruntime-common@npm:1.14.0"
  checksum: 10c0/f06f439ae1afabfdb10fb4d5b2f3af5085c8c13b04f77ed440592386a6c82d22bc845b153056522b39446fa65b16e9cb13670ca09501d5df9c23ec0f0ace6d3b
  languageName: node
  linkType: hard

"onnxruntime-node@npm:1.14.0":
  version: 1.14.0
  resolution: "onnxruntime-node@npm:1.14.0"
  dependencies:
    onnxruntime-common: "npm:~1.14.0"
  conditions: (os=win32 | os=darwin | os=linux)
  languageName: node
  linkType: hard

"onnxruntime-web@npm:1.14.0":
  version: 1.14.0
  resolution: "onnxruntime-web@npm:1.14.0"
  dependencies:
    flatbuffers: "npm:^1.12.0"
    guid-typescript: "npm:^1.0.9"
    long: "npm:^4.0.0"
    onnx-proto: "npm:^4.0.4"
    onnxruntime-common: "npm:~1.14.0"
    platform: "npm:^1.3.6"
  checksum: 10c0/f58a95d6d0dd0abc87a2beb2377926173ea189290009a15957e2bce14f42740382a59aaf596a8ab80f4d98e621dd336028389fb3a25a743c9c41d768761ca5b3
  languageName: node
  linkType: hard

"openai@npm:^4.96.0":
  version: 4.104.0
  resolution: "openai@npm:4.104.0"
  dependencies:
    "@types/node": "npm:^18.11.18"
    "@types/node-fetch": "npm:^2.6.4"
    abort-controller: "npm:^3.0.0"
    agentkeepalive: "npm:^4.2.1"
    form-data-encoder: "npm:1.7.2"
    formdata-node: "npm:^4.3.2"
    node-fetch: "npm:^2.6.7"
  peerDependencies:
    ws: ^8.18.0
    zod: ^3.23.8
  peerDependenciesMeta:
    ws:
      optional: true
    zod:
      optional: true
  bin:
    openai: bin/cli
  checksum: 10c0/c4f2e837684ed96b8cec58c65a584646d667c69918f29052775e2e8c05ff5c860d8b58214a7770bc6895ca8602480420c1db6a5392dd250179eb0b91c2b19a2f
  languageName: node
  linkType: hard

"openai@npm:^5.1.1":
  version: 5.1.1
  resolution: "openai@npm:5.1.1"
  peerDependencies:
    ws: ^8.18.0
    zod: ^3.23.8
  peerDependenciesMeta:
    ws:
      optional: true
    zod:
      optional: true
  bin:
    openai: bin/cli
  checksum: 10c0/5cb0223768ae3cb847e4f6d237f32761bf4091a569ebf4cec3b20a3a14be747d7735e4c683f8bc3358cf969423bd9f5935ce9b5e9f5edd6c2f30cf605be22b0a
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: "npm:^0.1.3"
    fast-levenshtein: "npm:^2.0.6"
    levn: "npm:^0.4.1"
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:^0.4.0"
    word-wrap: "npm:^1.2.5"
  checksum: 10c0/4afb687a059ee65b61df74dfe87d8d6815cd6883cb8b3d5883a910df72d0f5d029821f37025e4bccf4048873dbdb09acc6d303d27b8f76b1a80dd5a7d5334675
  languageName: node
  linkType: hard

"p-finally@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-finally@npm:1.0.0"
  checksum: 10c0/6b8552339a71fe7bd424d01d8451eea92d379a711fc62f6b2fe64cad8a472c7259a236c9a22b4733abca0b5666ad503cb497792a0478c5af31ded793d00937e7
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 10c0/9db675949dbdc9c3763c89e748d0ef8bdad0afbb24d49ceaf4c46c02c77d30db4e0652ed36d0a0a7a95154335fab810d95c86153105bb73b3a90448e2bb14e1a
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: "npm:^3.0.2"
  checksum: 10c0/2290d627ab7903b8b70d11d384fee714b797f6040d9278932754a6860845c4d3190603a0772a663c8cb5a7b21d1b16acb3a6487ebcafa9773094edc3dfe6009a
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10c0/46091610da2b38ce47bcd1d8b4835a6fa4e832848a6682cf1652bc93915770f4617afc844c10a77d1b3e56d2472bb2d5622353fa3ead01a7f42b04fc8e744a5c
  languageName: node
  linkType: hard

"p-queue@npm:^6.6.2":
  version: 6.6.2
  resolution: "p-queue@npm:6.6.2"
  dependencies:
    eventemitter3: "npm:^4.0.4"
    p-timeout: "npm:^3.2.0"
  checksum: 10c0/5739ecf5806bbeadf8e463793d5e3004d08bb3f6177bd1a44a005da8fd81bb90f80e4633e1fb6f1dfd35ee663a5c0229abe26aebb36f547ad5a858347c7b0d3e
  languageName: node
  linkType: hard

"p-retry@npm:4":
  version: 4.6.2
  resolution: "p-retry@npm:4.6.2"
  dependencies:
    "@types/retry": "npm:0.12.0"
    retry: "npm:^0.13.1"
  checksum: 10c0/d58512f120f1590cfedb4c2e0c42cb3fa66f3cea8a4646632fcb834c56055bb7a6f138aa57b20cc236fb207c9d694e362e0b5c2b14d9b062f67e8925580c73b0
  languageName: node
  linkType: hard

"p-timeout@npm:^3.2.0":
  version: 3.2.0
  resolution: "p-timeout@npm:3.2.0"
  dependencies:
    p-finally: "npm:^1.0.0"
  checksum: 10c0/524b393711a6ba8e1d48137c5924749f29c93d70b671e6db761afa784726572ca06149c715632da8f70c090073afb2af1c05730303f915604fd38ee207b70a61
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10c0/62ba2785eb655fec084a257af34dbe24292ab74516d6aecef97ef72d4897310bc6898f6c85b5cd22770eaa1ce60d55a0230e150fb6a966e3ecd6c511e23d164b
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: "npm:^3.0.0"
  checksum: 10c0/c63d6e80000d4babd11978e0d3fee386ca7752a02b035fd2435960ffaa7219dc42146f07069fb65e6e8bf1caef89daf9af7535a39bddf354d78bf50d8294f556
  languageName: node
  linkType: hard

"parse-entities@npm:^4.0.0":
  version: 4.0.2
  resolution: "parse-entities@npm:4.0.2"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    character-entities-legacy: "npm:^3.0.0"
    character-reference-invalid: "npm:^2.0.0"
    decode-named-character-reference: "npm:^1.0.0"
    is-alphanumerical: "npm:^2.0.0"
    is-decimal: "npm:^2.0.0"
    is-hexadecimal: "npm:^2.0.0"
  checksum: 10c0/a13906b1151750b78ed83d386294066daf5fb559e08c5af9591b2d98cc209123103016a01df776f65f8219ad26652d6d6b210d0974d452049cddfc53a8916c34
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 10c0/90dd4760d6f6174adb9f20cf0965ae12e23879b5f5464f38e92fce8073354341e4b3b76fa3d878351efe7d01e617121955284cfd002ab087fba1a0726ec0b4f5
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 10c0/8c0bd3f5238188197dc78dced15207a4716c51cc4e3624c44fc97acf69558f5ebb9a2afff486fe1b4ee148e0c133e96c5e11a9aa5c48a3006e3467da070e5e1b
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10c0/748c43efd5a569c039d7a00a03b58eecd1d75f3999f5a28303d75f521288df4823bc057d8784eb72358b2895a05f29a070bc9f1f17d28226cc4e62494cc58c4c
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10c0/32a13711a2a505616ae1cc1b5076801e453e7aae6ac40ab55b388bb91b9d0547a52f5aaceff710ea400205f18691120d4431e520afbe4266b836fadede15872d
  languageName: node
  linkType: hard

"path-to-regexp@npm:0.1.12":
  version: 0.1.12
  resolution: "path-to-regexp@npm:0.1.12"
  checksum: 10c0/1c6ff10ca169b773f3bba943bbc6a07182e332464704572962d277b900aeee81ac6aa5d060ff9e01149636c30b1f63af6e69dd7786ba6e0ddb39d4dee1f0645b
  languageName: node
  linkType: hard

"picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10c0/e2e3e8170ab9d7c7421969adaa7e1b31434f789afb9b3f115f6b96d91945041ac3ceb02e9ec6fe6510ff036bcc0bf91e69a1772edc0b707e12b19c0f2d6bcf58
  languageName: node
  linkType: hard

"picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10c0/26c02b8d06f03206fc2ab8d16f19960f2ff9e81a658f831ecb656d8f17d9edc799e8364b1f4a7873e89d9702dff96204be0fa26fe4181f6843f040f819dac4be
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: 10c0/7c51f3ad2bb42c776f49ebf964c644958158be30d0a510efd5a395e8d49cb5acfed5b82c0c5b365523ce18e6ab85013c9ebe574f60305892ec3fa8eee8304ccc
  languageName: node
  linkType: hard

"platform@npm:^1.3.6":
  version: 1.3.6
  resolution: "platform@npm:1.3.6"
  checksum: 10c0/69f2eb692e15f1a343dd0d9347babd9ca933824c8673096be746ff66f99f2bdc909fadd8609076132e6ec768349080babb7362299f2a7f885b98f1254ae6224b
  languageName: node
  linkType: hard

"postcss@npm:^8.5.3":
  version: 8.5.4
  resolution: "postcss@npm:8.5.4"
  dependencies:
    nanoid: "npm:^3.3.11"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10c0/0feff648614a834f7cd5396ea6b05b658ca0507e10a4eaad03b56c348f6aec93f42a885fc1b30522630c6a7e49ae53b38a061e3cba526f2d9857afbe095a22bb
  languageName: node
  linkType: hard

"prebuild-install@npm:^7.1.1":
  version: 7.1.3
  resolution: "prebuild-install@npm:7.1.3"
  dependencies:
    detect-libc: "npm:^2.0.0"
    expand-template: "npm:^2.0.3"
    github-from-package: "npm:0.0.0"
    minimist: "npm:^1.2.3"
    mkdirp-classic: "npm:^0.5.3"
    napi-build-utils: "npm:^2.0.0"
    node-abi: "npm:^3.3.0"
    pump: "npm:^3.0.0"
    rc: "npm:^1.2.7"
    simple-get: "npm:^4.0.0"
    tar-fs: "npm:^2.0.0"
    tunnel-agent: "npm:^0.6.0"
  bin:
    prebuild-install: bin.js
  checksum: 10c0/25919a42b52734606a4036ab492d37cfe8b601273d8dfb1fa3c84e141a0a475e7bad3ab848c741d2f810cef892fcf6059b8c7fe5b29f98d30e0c29ad009bedff
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: 10c0/b00d617431e7886c520a6f498a2e14c75ec58f6d93ba48c3b639cf241b54232d90daa05d83a9e9b9fef6baa63cb7e1e4602c2372fea5bc169668401eb127d0cd
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10c0/bbe5edb944b0ad63387a1d5b1911ae93e05ce8d0f60de1035b218cdcceedfe39dbd2c697853355b70f1a090f8f58fe90da487c85216bf9671f9499d1a897e9e3
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10c0/9c7045a1a2928094b5b9b15336dcd2a7b1c052f674550df63cc3f36cd44028e5080448175b6f6ca32b642de81150f5e7b1a98b728f15cb069f2dd60ac2616b96
  languageName: node
  linkType: hard

"property-information@npm:^7.0.0":
  version: 7.1.0
  resolution: "property-information@npm:7.1.0"
  checksum: 10c0/e0fe22cff26103260ad0e82959229106563fa115a54c4d6c183f49d88054e489cc9f23452d3ad584179dc13a8b7b37411a5df873746b5e4086c865874bfa968e
  languageName: node
  linkType: hard

"protobufjs@npm:^6.8.8":
  version: 6.11.4
  resolution: "protobufjs@npm:6.11.4"
  dependencies:
    "@protobufjs/aspromise": "npm:^1.1.2"
    "@protobufjs/base64": "npm:^1.1.2"
    "@protobufjs/codegen": "npm:^2.0.4"
    "@protobufjs/eventemitter": "npm:^1.1.0"
    "@protobufjs/fetch": "npm:^1.1.0"
    "@protobufjs/float": "npm:^1.0.2"
    "@protobufjs/inquire": "npm:^1.1.0"
    "@protobufjs/path": "npm:^1.1.2"
    "@protobufjs/pool": "npm:^1.1.0"
    "@protobufjs/utf8": "npm:^1.1.0"
    "@types/long": "npm:^4.0.1"
    "@types/node": "npm:>=13.7.0"
    long: "npm:^4.0.0"
  bin:
    pbjs: bin/pbjs
    pbts: bin/pbts
  checksum: 10c0/c244d7b9b6d3258193da5c0d1e558dfb47f208ae345e209f90ec45c9dca911b90fa17e937892a9a39a4136ab9886981aae9efdf6039f7baff4f7225f5eeb9812
  languageName: node
  linkType: hard

"proxy-addr@npm:~2.0.7":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: "npm:0.2.0"
    ipaddr.js: "npm:1.9.1"
  checksum: 10c0/c3eed999781a35f7fd935f398b6d8920b6fb00bbc14287bc6de78128ccc1a02c89b95b56742bf7cf0362cc333c61d138532049c7dedc7a328ef13343eff81210
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.2
  resolution: "pump@npm:3.0.2"
  dependencies:
    end-of-stream: "npm:^1.1.0"
    once: "npm:^1.3.1"
  checksum: 10c0/5ad655cb2a7738b4bcf6406b24ad0970d680649d996b55ad20d1be8e0c02394034e4c45ff7cd105d87f1e9b96a0e3d06fd28e11fae8875da26e7f7a8e2c9726f
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 10c0/14f76a8206bc3464f794fb2e3d3cc665ae416c01893ad7a02b23766eb07159144ee612ad67af5e84fa4479ccfe67678c4feb126b0485651b302babf66f04f9e9
  languageName: node
  linkType: hard

"qs@npm:6.13.0":
  version: 6.13.0
  resolution: "qs@npm:6.13.0"
  dependencies:
    side-channel: "npm:^1.0.6"
  checksum: 10c0/62372cdeec24dc83a9fb240b7533c0fdcf0c5f7e0b83343edd7310f0ab4c8205a5e7c56406531f2e47e1b4878a3821d652be4192c841de5b032ca83619d8f860
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10c0/900a93d3cdae3acd7d16f642c29a642aea32c2026446151f0778c62ac089d4b8e6c986811076e1ae180a694cedf077d453a11b58ff0a865629a4f82ab558e102
  languageName: node
  linkType: hard

"range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 10c0/96c032ac2475c8027b7a4e9fe22dc0dfe0f6d90b85e496e0f016fbdb99d6d066de0112e680805075bd989905e2123b3b3d002765149294dce0c1f7f01fcc2ea0
  languageName: node
  linkType: hard

"raw-body@npm:2.5.2":
  version: 2.5.2
  resolution: "raw-body@npm:2.5.2"
  dependencies:
    bytes: "npm:3.1.2"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    unpipe: "npm:1.0.0"
  checksum: 10c0/b201c4b66049369a60e766318caff5cb3cc5a900efd89bdac431463822d976ad0670912c931fdbdcf5543207daf6f6833bca57aa116e1661d2ea91e12ca692c4
  languageName: node
  linkType: hard

"rc@npm:^1.2.7":
  version: 1.2.8
  resolution: "rc@npm:1.2.8"
  dependencies:
    deep-extend: "npm:^0.6.0"
    ini: "npm:~1.3.0"
    minimist: "npm:^1.2.0"
    strip-json-comments: "npm:~2.0.1"
  bin:
    rc: ./cli.js
  checksum: 10c0/24a07653150f0d9ac7168e52943cc3cb4b7a22c0e43c7dff3219977c2fdca5a2760a304a029c20811a0e79d351f57d46c9bde216193a0f73978496afc2b85b15
  languageName: node
  linkType: hard

"react-dom@npm:^19.0.0":
  version: 19.1.0
  resolution: "react-dom@npm:19.1.0"
  dependencies:
    scheduler: "npm:^0.26.0"
  peerDependencies:
    react: ^19.1.0
  checksum: 10c0/3e26e89bb6c67c9a6aa86cb888c7a7f8258f2e347a6d2a15299c17eb16e04c19194e3452bc3255bd34000a61e45e2cb51e46292392340432f133e5a5d2dfb5fc
  languageName: node
  linkType: hard

"react-icons@npm:^5.5.0":
  version: 5.5.0
  resolution: "react-icons@npm:5.5.0"
  peerDependencies:
    react: "*"
  checksum: 10c0/a24309bfc993c19cbcbfc928157e53a137851822779977b9588f6dd41ffc4d11ebc98b447f4039b0d309a858f0a42980f6bfb4477fb19f9f2d1bc2e190fcf79c
  languageName: node
  linkType: hard

"react-markdown@npm:^10.1.0":
  version: 10.1.0
  resolution: "react-markdown@npm:10.1.0"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    "@types/mdast": "npm:^4.0.0"
    devlop: "npm:^1.0.0"
    hast-util-to-jsx-runtime: "npm:^2.0.0"
    html-url-attributes: "npm:^3.0.0"
    mdast-util-to-hast: "npm:^13.0.0"
    remark-parse: "npm:^11.0.0"
    remark-rehype: "npm:^11.0.0"
    unified: "npm:^11.0.0"
    unist-util-visit: "npm:^5.0.0"
    vfile: "npm:^6.0.0"
  peerDependencies:
    "@types/react": ">=18"
    react: ">=18"
  checksum: 10c0/4a5dc7d15ca6d05e9ee95318c1904f83b111a76f7588c44f50f1d54d4c97193b84e4f64c4b592057c989228238a2590306cedd0c4d398e75da49262b2b5ae1bf
  languageName: node
  linkType: hard

"react-refresh@npm:^0.17.0":
  version: 0.17.0
  resolution: "react-refresh@npm:0.17.0"
  checksum: 10c0/002cba940384c9930008c0bce26cac97a9d5682bc623112c2268ba0c155127d9c178a9a5cc2212d560088d60dfd503edd808669a25f9b377f316a32361d0b23c
  languageName: node
  linkType: hard

"react-remove-scroll-bar@npm:^2.3.7":
  version: 2.3.8
  resolution: "react-remove-scroll-bar@npm:2.3.8"
  dependencies:
    react-style-singleton: "npm:^2.2.2"
    tslib: "npm:^2.0.0"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/9a0675c66cbb52c325bdbfaed80987a829c4504cefd8ff2dd3b6b3afc9a1500b8ec57b212e92c1fb654396d07bbe18830a8146fe77677d2a29ce40b5e1f78654
  languageName: node
  linkType: hard

"react-remove-scroll@npm:^2.6.3":
  version: 2.7.1
  resolution: "react-remove-scroll@npm:2.7.1"
  dependencies:
    react-remove-scroll-bar: "npm:^2.3.7"
    react-style-singleton: "npm:^2.2.3"
    tslib: "npm:^2.1.0"
    use-callback-ref: "npm:^1.3.3"
    use-sidecar: "npm:^1.1.3"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/7ad8f6ffd3e2aedf9b3d79f0c9088a9a3d7c5332d80c923427a6d97fe0626fb4cb33a6d9174d19fad57d860be69c96f68497a0619c3a8af0e8a5332e49bdde31
  languageName: node
  linkType: hard

"react-speech-recognition@npm:^4.0.0":
  version: 4.0.1
  resolution: "react-speech-recognition@npm:4.0.1"
  dependencies:
    lodash.debounce: "npm:^4.0.8"
  peerDependencies:
    react: ">=16.8.0"
  checksum: 10c0/26c90913b34858aefae60df387d49c80556f52430bf61c3aa7f703794139a969b66fd4e8ba11c805dee0dfeff775eb085e2dcf7a0f46f45809d3a770c92171e4
  languageName: node
  linkType: hard

"react-style-singleton@npm:^2.2.2, react-style-singleton@npm:^2.2.3":
  version: 2.2.3
  resolution: "react-style-singleton@npm:2.2.3"
  dependencies:
    get-nonce: "npm:^1.0.0"
    tslib: "npm:^2.0.0"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/841938ff16d16a6b76895f4cb2e1fea957e5fe3b30febbf03a54892dae1c9153f2383e231dea0b3ba41192ad2f2849448fa859caccd288943bce32639e971bee
  languageName: node
  linkType: hard

"react-toastify@npm:^11.0.5":
  version: 11.0.5
  resolution: "react-toastify@npm:11.0.5"
  dependencies:
    clsx: "npm:^2.1.1"
  peerDependencies:
    react: ^18 || ^19
    react-dom: ^18 || ^19
  checksum: 10c0/50f5b81323ebb1957b2efd0963fac24aa1407155d16ab756ffd6d0f42f8af17e796b3958a9fce13e9d1b945d6c3a5a9ebf13529478474d8a2af4bf1dd0db67d2
  languageName: node
  linkType: hard

"react-universal-interface@npm:^0.6.2":
  version: 0.6.2
  resolution: "react-universal-interface@npm:0.6.2"
  peerDependencies:
    react: "*"
    tslib: "*"
  checksum: 10c0/97c32ecb7a425c3bcaa92dcf84c46146b49610d928efde9e9ee5518c475a0db942f01634dd490e4f42fcd95cc2f49657c1b96dcef96423c06f077147fe1968ab
  languageName: node
  linkType: hard

"react-use@npm:^17.6.0":
  version: 17.6.0
  resolution: "react-use@npm:17.6.0"
  dependencies:
    "@types/js-cookie": "npm:^2.2.6"
    "@xobotyi/scrollbar-width": "npm:^1.9.5"
    copy-to-clipboard: "npm:^3.3.1"
    fast-deep-equal: "npm:^3.1.3"
    fast-shallow-equal: "npm:^1.0.0"
    js-cookie: "npm:^2.2.1"
    nano-css: "npm:^5.6.2"
    react-universal-interface: "npm:^0.6.2"
    resize-observer-polyfill: "npm:^1.5.1"
    screenfull: "npm:^5.1.0"
    set-harmonic-interval: "npm:^1.0.1"
    throttle-debounce: "npm:^3.0.1"
    ts-easing: "npm:^0.2.0"
    tslib: "npm:^2.1.0"
  peerDependencies:
    react: "*"
    react-dom: "*"
  checksum: 10c0/d122199f3edd056bfd866837b0f19a44366e77c7535c6c2c5eb5f400409eae4c9b1fe73c9d35073c8434080eee388ca8fe49a68d09d6f794ccaa35a4ae2112a9
  languageName: node
  linkType: hard

"react@npm:^19.0.0":
  version: 19.1.0
  resolution: "react@npm:19.1.0"
  checksum: 10c0/530fb9a62237d54137a13d2cfb67a7db6a2156faed43eecc423f4713d9b20c6f2728b026b45e28fcd72e8eadb9e9ed4b089e99f5e295d2f0ad3134251bdd3698
  languageName: node
  linkType: hard

"readable-stream@npm:^3.1.1, readable-stream@npm:^3.4.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: "npm:^2.0.3"
    string_decoder: "npm:^1.1.1"
    util-deprecate: "npm:^1.0.1"
  checksum: 10c0/e37be5c79c376fdd088a45fa31ea2e423e5d48854be7a22a58869b4e84d25047b193f6acb54f1012331e1bcd667ffb569c01b99d36b0bd59658fb33f513511b7
  languageName: node
  linkType: hard

"remark-parse@npm:^11.0.0":
  version: 11.0.0
  resolution: "remark-parse@npm:11.0.0"
  dependencies:
    "@types/mdast": "npm:^4.0.0"
    mdast-util-from-markdown: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
    unified: "npm:^11.0.0"
  checksum: 10c0/6eed15ddb8680eca93e04fcb2d1b8db65a743dcc0023f5007265dda558b09db595a087f622062ccad2630953cd5cddc1055ce491d25a81f3317c858348a8dd38
  languageName: node
  linkType: hard

"remark-rehype@npm:^11.0.0":
  version: 11.1.2
  resolution: "remark-rehype@npm:11.1.2"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    "@types/mdast": "npm:^4.0.0"
    mdast-util-to-hast: "npm:^13.0.0"
    unified: "npm:^11.0.0"
    vfile: "npm:^6.0.0"
  checksum: 10c0/f9eccacfb596d9605581dc05bfad28635d6ded5dd0a18e88af5fd4df0d3fcf9612e1501d4513bc2164d833cfe9636dab20400080b09e53f155c6e1442a1231fb
  languageName: node
  linkType: hard

"resize-observer-polyfill@npm:^1.5.1":
  version: 1.5.1
  resolution: "resize-observer-polyfill@npm:1.5.1"
  checksum: 10c0/5e882475067f0b97dc07e0f37c3e335ac5bc3520d463f777cec7e894bb273eddbfecb857ae668e6fb6881fd6f6bb7148246967172139302da50fa12ea3a15d95
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 10c0/8408eec31a3112ef96e3746c37be7d64020cda07c03a920f5024e77290a218ea758b26ca9529fd7b1ad283947f34b2291c1c0f6aa0ed34acfdda9c6014c8d190
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10c0/59933e8501727ba13ad73ef4a04d5280b3717fd650408460c987392efe9d7be2040778ed8ebe933c5cbd63da3dcc37919c141ef8af0a54a6e4fca5a2af177bfe
  languageName: node
  linkType: hard

"retry@npm:^0.13.1":
  version: 0.13.1
  resolution: "retry@npm:0.13.1"
  checksum: 10c0/9ae822ee19db2163497e074ea919780b1efa00431d197c7afdb950e42bf109196774b92a49fc9821f0b8b328a98eea6017410bfc5e8a0fc19c85c6d11adb3772
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.1.0
  resolution: "reusify@npm:1.1.0"
  checksum: 10c0/4eff0d4a5f9383566c7d7ec437b671cc51b25963bd61bf127c3f3d3f68e44a026d99b8d2f1ad344afff8d278a8fe70a8ea092650a716d22287e8bef7126bb2fa
  languageName: node
  linkType: hard

"rollup@npm:^4.34.9":
  version: 4.42.0
  resolution: "rollup@npm:4.42.0"
  dependencies:
    "@rollup/rollup-android-arm-eabi": "npm:4.42.0"
    "@rollup/rollup-android-arm64": "npm:4.42.0"
    "@rollup/rollup-darwin-arm64": "npm:4.42.0"
    "@rollup/rollup-darwin-x64": "npm:4.42.0"
    "@rollup/rollup-freebsd-arm64": "npm:4.42.0"
    "@rollup/rollup-freebsd-x64": "npm:4.42.0"
    "@rollup/rollup-linux-arm-gnueabihf": "npm:4.42.0"
    "@rollup/rollup-linux-arm-musleabihf": "npm:4.42.0"
    "@rollup/rollup-linux-arm64-gnu": "npm:4.42.0"
    "@rollup/rollup-linux-arm64-musl": "npm:4.42.0"
    "@rollup/rollup-linux-loongarch64-gnu": "npm:4.42.0"
    "@rollup/rollup-linux-powerpc64le-gnu": "npm:4.42.0"
    "@rollup/rollup-linux-riscv64-gnu": "npm:4.42.0"
    "@rollup/rollup-linux-riscv64-musl": "npm:4.42.0"
    "@rollup/rollup-linux-s390x-gnu": "npm:4.42.0"
    "@rollup/rollup-linux-x64-gnu": "npm:4.42.0"
    "@rollup/rollup-linux-x64-musl": "npm:4.42.0"
    "@rollup/rollup-win32-arm64-msvc": "npm:4.42.0"
    "@rollup/rollup-win32-ia32-msvc": "npm:4.42.0"
    "@rollup/rollup-win32-x64-msvc": "npm:4.42.0"
    "@types/estree": "npm:1.0.7"
    fsevents: "npm:~2.3.2"
  dependenciesMeta:
    "@rollup/rollup-android-arm-eabi":
      optional: true
    "@rollup/rollup-android-arm64":
      optional: true
    "@rollup/rollup-darwin-arm64":
      optional: true
    "@rollup/rollup-darwin-x64":
      optional: true
    "@rollup/rollup-freebsd-arm64":
      optional: true
    "@rollup/rollup-freebsd-x64":
      optional: true
    "@rollup/rollup-linux-arm-gnueabihf":
      optional: true
    "@rollup/rollup-linux-arm-musleabihf":
      optional: true
    "@rollup/rollup-linux-arm64-gnu":
      optional: true
    "@rollup/rollup-linux-arm64-musl":
      optional: true
    "@rollup/rollup-linux-loongarch64-gnu":
      optional: true
    "@rollup/rollup-linux-powerpc64le-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-musl":
      optional: true
    "@rollup/rollup-linux-s390x-gnu":
      optional: true
    "@rollup/rollup-linux-x64-gnu":
      optional: true
    "@rollup/rollup-linux-x64-musl":
      optional: true
    "@rollup/rollup-win32-arm64-msvc":
      optional: true
    "@rollup/rollup-win32-ia32-msvc":
      optional: true
    "@rollup/rollup-win32-x64-msvc":
      optional: true
    fsevents:
      optional: true
  bin:
    rollup: dist/bin/rollup
  checksum: 10c0/160fdb0874af5f0f619987b4e9abb3b136fc154f759762bfde4d65d864d6d06594ae7d1d8e6d4558d1b8ef329aaa6a8de543e90feead3d872db15cf61f78426c
  languageName: node
  linkType: hard

"rtl-css-js@npm:^1.16.1":
  version: 1.16.1
  resolution: "rtl-css-js@npm:1.16.1"
  dependencies:
    "@babel/runtime": "npm:^7.1.2"
  checksum: 10c0/4b81ef50e50c97455d61c9bb576e2892651c79bac5d0c52b4123ebb9d6a2c5144590a79c9db0a3212a81b4eb83bf317e03637220f20b387a37b96cbac324d3d2
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10c0/200b5ab25b5b8b7113f9901bfe3afc347e19bb7475b267d55ad0eb86a62a46d77510cb0f232507c9e5d497ebda569a08a9867d0d14f57a82ad5564d991588b39
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1, safe-buffer@npm:^5.0.1, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10c0/6501914237c0a86e9675d4e51d89ca3c21ffd6a31642efeba25ad65720bce6921c9e7e974e5be91a786b25aa058b5303285d3c15dbabf983a919f5f630d349f3
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10c0/7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"scheduler@npm:^0.26.0":
  version: 0.26.0
  resolution: "scheduler@npm:0.26.0"
  checksum: 10c0/5b8d5bfddaae3513410eda54f2268e98a376a429931921a81b5c3a2873aab7ca4d775a8caac5498f8cbc7d0daeab947cf923dbd8e215d61671f9f4e392d34356
  languageName: node
  linkType: hard

"screenfull@npm:^5.1.0":
  version: 5.2.0
  resolution: "screenfull@npm:5.2.0"
  checksum: 10c0/86fd49983e2edc153ee2e674a570c711cb0961a9cacca659309f79636ccc8ca8a0b830ea4dacdae7403a8bb7ba6affd5bcdce053aa97782961247a49bfd2ba68
  languageName: node
  linkType: hard

"semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 10c0/e3d79b609071caa78bcb6ce2ad81c7966a46a7431d9d58b8800cfa9cb6a63699b3899a0e4bcce36167a284578212d9ae6942b6929ba4aa5015c079a67751d42d
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.5.4, semver@npm:^7.6.0, semver@npm:^7.6.3":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: 10c0/aca305edfbf2383c22571cb7714f48cadc7ac95371b4b52362fb8eeffdfbc0de0669368b82b2b15978f8848f01d7114da65697e56cd8c37b0dab8c58e543f9ea
  languageName: node
  linkType: hard

"send@npm:0.19.0":
  version: 0.19.0
  resolution: "send@npm:0.19.0"
  dependencies:
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    mime: "npm:1.6.0"
    ms: "npm:2.1.3"
    on-finished: "npm:2.4.1"
    range-parser: "npm:~1.2.1"
    statuses: "npm:2.0.1"
  checksum: 10c0/ea3f8a67a8f0be3d6bf9080f0baed6d2c51d11d4f7b4470de96a5029c598a7011c497511ccc28968b70ef05508675cebff27da9151dd2ceadd60be4e6cf845e3
  languageName: node
  linkType: hard

"serve-static@npm:1.16.2":
  version: 1.16.2
  resolution: "serve-static@npm:1.16.2"
  dependencies:
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    parseurl: "npm:~1.3.3"
    send: "npm:0.19.0"
  checksum: 10c0/528fff6f5e12d0c5a391229ad893910709bc51b5705962b09404a1d813857578149b8815f35d3ee5752f44cd378d0f31669d4b1d7e2d11f41e08283d5134bd1f
  languageName: node
  linkType: hard

"set-harmonic-interval@npm:^1.0.1":
  version: 1.0.1
  resolution: "set-harmonic-interval@npm:1.0.1"
  checksum: 10c0/49014d928a62c8418507bf66ffef7066783e8fb19f76e955318bbae5a8c4b56e1a7176b370f9040ef9de51531aa522a3f96fa5c47b1534635aa577ff7c12f9c6
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: 10c0/68733173026766fa0d9ecaeb07f0483f4c2dc70ca376b3b7c40b7cda909f94b0918f6c5ad5ce27a9160bdfb475efaa9d5e705a11d8eaae18f9835d20976028bc
  languageName: node
  linkType: hard

"sharp@npm:^0.32.0":
  version: 0.32.6
  resolution: "sharp@npm:0.32.6"
  dependencies:
    color: "npm:^4.2.3"
    detect-libc: "npm:^2.0.2"
    node-addon-api: "npm:^6.1.0"
    node-gyp: "npm:latest"
    prebuild-install: "npm:^7.1.1"
    semver: "npm:^7.5.4"
    simple-get: "npm:^4.0.1"
    tar-fs: "npm:^3.0.4"
    tunnel-agent: "npm:^0.6.0"
  checksum: 10c0/f6a756fec5051ef2f9341e0543cde1da4e822982dd5398010baad92e2262bd177e08b753eb19b2fbee30f2fcb0e8756f24088fafc48293a364e9a8f8dc65a300
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10c0/a41692e7d89a553ef21d324a5cceb5f686d1f3c040759c50aab69688634688c5c327f26f3ecf7001ebfd78c01f3c7c0a11a7c8bfd0a8bc9f6240d4f40b224e4e
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10c0/1dbed0726dd0e1152a92696c76c7f06084eb32a90f0528d11acd764043aacf76994b2fb30aa1291a21bd019d6699164d048286309a278855ee7bec06cf6fb690
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/644f4ac893456c9490ff388bf78aea9d333d5e5bfc64cfb84be8f04bf31ddc111a8d4b83b85d7e7e8a7b845bc185a9ad02c052d20e086983cf59f0be517d9b3d
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/010584e6444dd8a20b85bc926d934424bd809e1a3af941cace229f7fdcb751aada0fb7164f60c2e22292b7fa3c0ff0bce237081fd4cdbc80de1dc68e95430672
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
    side-channel-map: "npm:^1.0.1"
  checksum: 10c0/71362709ac233e08807ccd980101c3e2d7efe849edc51455030327b059f6c4d292c237f94dc0685031dd11c07dd17a68afde235d6cf2102d949567f98ab58185
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.6":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
    side-channel-list: "npm:^1.0.0"
    side-channel-map: "npm:^1.0.1"
    side-channel-weakmap: "npm:^1.0.2"
  checksum: 10c0/cb20dad41eb032e6c24c0982e1e5a24963a28aa6122b4f05b3f3d6bf8ae7fd5474ef382c8f54a6a3ab86e0cac4d41a23bd64ede3970e5bfb50326ba02a7996e6
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10c0/41602dce540e46d599edba9d9860193398d135f7ff72cab629db5171516cfae628d21e7bfccde1bbfdf11c48726bc2a6d1a8fb8701125852fbfda7cf19c6aa83
  languageName: node
  linkType: hard

"simple-concat@npm:^1.0.0":
  version: 1.0.1
  resolution: "simple-concat@npm:1.0.1"
  checksum: 10c0/62f7508e674414008910b5397c1811941d457dfa0db4fd5aa7fa0409eb02c3609608dfcd7508cace75b3a0bf67a2a77990711e32cd213d2c76f4fd12ee86d776
  languageName: node
  linkType: hard

"simple-get@npm:^4.0.0, simple-get@npm:^4.0.1":
  version: 4.0.1
  resolution: "simple-get@npm:4.0.1"
  dependencies:
    decompress-response: "npm:^6.0.0"
    once: "npm:^1.3.1"
    simple-concat: "npm:^1.0.0"
  checksum: 10c0/b0649a581dbca741babb960423248899203165769747142033479a7dc5e77d7b0fced0253c731cd57cf21e31e4d77c9157c3069f4448d558ebc96cf9e1eebcf0
  languageName: node
  linkType: hard

"simple-swizzle@npm:^0.2.2":
  version: 0.2.2
  resolution: "simple-swizzle@npm:0.2.2"
  dependencies:
    is-arrayish: "npm:^0.3.1"
  checksum: 10c0/df5e4662a8c750bdba69af4e8263c5d96fe4cd0f9fe4bdfa3cbdeb45d2e869dff640beaaeb1ef0e99db4d8d2ec92f85508c269f50c972174851bc1ae5bd64308
  languageName: node
  linkType: hard

"simple-wcswidth@npm:^1.0.1":
  version: 1.0.1
  resolution: "simple-wcswidth@npm:1.0.1"
  checksum: 10c0/2befead4c97134424aa3fba593a81daa9934fd61b9e4c65374b57ac5eecc2f2be1984b017bbdbc919923e19b77f2fcbdb94434789b9643fa8c3fde3a2a6a4b6f
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10c0/a16775323e1404dd43fabafe7460be13a471e021637bc7889468eb45ce6a6b207261f454e4e530a19500cc962c4cc5348583520843b363f4193cee5c00e1e539
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10c0/5d2c6cecba6821389aabf18728325730504bf9bb1d9e342e7987a5d13badd7a98838cc9a55b8ed3cb866ad37cc23e1086f09c4d72d93105ce9dfe76330e9d2a6
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.4
  resolution: "socks@npm:2.8.4"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10c0/00c3271e233ccf1fb83a3dd2060b94cc37817e0f797a93c560b9a7a86c4a0ec2961fb31263bdd24a3c28945e24868b5f063cd98744171d9e942c513454b50ae5
  languageName: node
  linkType: hard

"sonner@npm:^2.0.3":
  version: 2.0.5
  resolution: "sonner@npm:2.0.5"
  peerDependencies:
    react: ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    react-dom: ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  checksum: 10c0/38ec98e2f5d7e086825307f737a90bdc8639182d184e002719c2368bf3a9259c340f41afda731716d2b78c40e5e3aa9165058375be42f6a93bda0876b9b433ba
  languageName: node
  linkType: hard

"source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 10c0/7bda1fc4c197e3c6ff17de1b8b2c20e60af81b63a52cb32ec5a5d67a20a7d42651e2cb34ebe93833c5a2a084377e17455854fee3e21e7925c64a51b6a52b0faf
  languageName: node
  linkType: hard

"source-map@npm:0.5.6":
  version: 0.5.6
  resolution: "source-map@npm:0.5.6"
  checksum: 10c0/beb2c5974bb58954d75e86249953d47ae16f7df1a8531abb9fcae0cd262d9fa09c2db3a134e20e99358b1adba42b6b054a32c8e16b571b3efcf6af644c329f0d
  languageName: node
  linkType: hard

"source-map@npm:^0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 10c0/ab55398007c5e5532957cb0beee2368529618ac0ab372d789806f5718123cc4367d57de3904b4e6a4170eb5a0b0f41373066d02ca0735a0c4d75c7d328d3e011
  languageName: node
  linkType: hard

"space-separated-tokens@npm:^2.0.0":
  version: 2.0.2
  resolution: "space-separated-tokens@npm:2.0.2"
  checksum: 10c0/6173e1d903dca41dcab6a2deed8b4caf61bd13b6d7af8374713500570aa929ff9414ae09a0519f4f8772df993300305a395d4871f35bc4ca72b6db57e1f30af8
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10c0/09270dc4f30d479e666aee820eacd9e464215cdff53848b443964202bf4051490538e5dd1b42e1a65cf7296916ca17640aebf63dae9812749c7542ee5f288dec
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/caddd5f544b2006e88fa6b0124d8d7b28208b83c72d7672d5ade44d794525d23b540f3396108c4eb9280dcb7c01f0bef50682f5b4b2c34291f7c5e211fd1417d
  languageName: node
  linkType: hard

"stack-generator@npm:^2.0.5":
  version: 2.0.10
  resolution: "stack-generator@npm:2.0.10"
  dependencies:
    stackframe: "npm:^1.3.4"
  checksum: 10c0/c3f6f6c580488e65c0fee806a57f6ae4b79e6435f144be471c1f20328a8d9d8492d4f3beed31840f6dae03e2633325e2764fd3aca5c3126a0639e7c9ddfa45ce
  languageName: node
  linkType: hard

"stackframe@npm:^1.3.4":
  version: 1.3.4
  resolution: "stackframe@npm:1.3.4"
  checksum: 10c0/18410f7a1e0c5d211a4effa83bdbf24adbe8faa8c34db52e1cd3e89837518c592be60b60d8b7270ac53eeeb8b807cd11b399a41667f6c9abb41059c3ccc8a989
  languageName: node
  linkType: hard

"stacktrace-gps@npm:^3.0.4":
  version: 3.1.2
  resolution: "stacktrace-gps@npm:3.1.2"
  dependencies:
    source-map: "npm:0.5.6"
    stackframe: "npm:^1.3.4"
  checksum: 10c0/0dcc1aa46e364a2b4d1eabce4777fecf337576a11ee3cfc92f07b9ec79ccb76810752431eeb9771289d250d0bb58dbe19a178b96bf7b2e9f773334d03aa96bb9
  languageName: node
  linkType: hard

"stacktrace-js@npm:^2.0.2":
  version: 2.0.2
  resolution: "stacktrace-js@npm:2.0.2"
  dependencies:
    error-stack-parser: "npm:^2.0.6"
    stack-generator: "npm:^2.0.5"
    stacktrace-gps: "npm:^3.0.4"
  checksum: 10c0/9a10c222524ca03690bcb27437b39039885223e39320367f2be36e6f750c2d198ae99189869a22c255bf60072631eb609d47e8e33661e95133686904e01121ec
  languageName: node
  linkType: hard

"state-local@npm:^1.0.6":
  version: 1.0.7
  resolution: "state-local@npm:1.0.7"
  checksum: 10c0/8dc7daeac71844452fafb514a6d6b6f40d7e2b33df398309ea1c7b3948d6110c57f112b7196500a10c54fdde40291488c52c875575670fb5c819602deca48bd9
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 10c0/34378b207a1620a24804ce8b5d230fea0c279f00b18a7209646d5d47e419d1cc23e7cbf33a25a1e51ac38973dc2ac2e1e9c647a8e481ef365f77668d72becfd0
  languageName: node
  linkType: hard

"streamx@npm:^2.15.0, streamx@npm:^2.21.0":
  version: 2.22.1
  resolution: "streamx@npm:2.22.1"
  dependencies:
    bare-events: "npm:^2.2.0"
    fast-fifo: "npm:^1.3.2"
    text-decoder: "npm:^1.1.0"
  dependenciesMeta:
    bare-events:
      optional: true
  checksum: 10c0/b5e489cca78ff23b910e7d58c3e0059e692f93ec401a5974689f2c50c33c9d94f64246a305566ad52cdb818ee583e02e4257b9066fd654cb9f576a9692fdb976
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10c0/1e525e92e5eae0afd7454086eed9c818ee84374bb80328fc41217ae72ff5f065ef1c9d7f72da41de40c75fa8bb3dee63d92373fd492c84260a552c636392a47b
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/ab9c4264443d35b8b923cbdd513a089a60de339216d3b0ed3be3ba57d6880e1a192b70ae17225f764d7adbf5994e9bb8df253a944736c15a0240eff553c678ca
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: "npm:~5.2.0"
  checksum: 10c0/810614ddb030e271cd591935dcd5956b2410dd079d64ff92a1844d6b7588bf992b3e1b69b0f4d34a3e06e0bd73046ac646b5264c1987b20d0601f81ef35d731d
  languageName: node
  linkType: hard

"stringify-entities@npm:^4.0.0":
  version: 4.0.4
  resolution: "stringify-entities@npm:4.0.4"
  dependencies:
    character-entities-html4: "npm:^2.0.0"
    character-entities-legacy: "npm:^3.0.0"
  checksum: 10c0/537c7e656354192406bdd08157d759cd615724e9d0873602d2c9b2f6a5c0a8d0b1d73a0a08677848105c5eebac6db037b57c0b3a4ec86331117fa7319ed50448
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10c0/1ae5f212a126fe5b167707f716942490e3933085a5ff6c008ab97ab2f272c8025d3aa218b7bd6ab25729ca20cc81cddb252102f8751e13482a5199e873680952
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10c0/a198c3762e8832505328cbf9e8c8381de14a4fa50a4f9b2160138158ea88c0f5549fb50cb13c651c3088f47e63a108b34622ec18c0499b6c8c3a5ddf6b305ac4
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 10c0/9681a6257b925a7fa0f285851c0e613cc934a50661fa7bb41ca9cbbff89686bb4a0ee366e6ecedc4daafd01e83eee0720111ab294366fe7c185e935475ebcecd
  languageName: node
  linkType: hard

"strip-json-comments@npm:~2.0.1":
  version: 2.0.1
  resolution: "strip-json-comments@npm:2.0.1"
  checksum: 10c0/b509231cbdee45064ff4f9fd73609e2bcc4e84a4d508e9dd0f31f70356473fde18abfb5838c17d56fb236f5a06b102ef115438de0600b749e818a35fbbc48c43
  languageName: node
  linkType: hard

"strnum@npm:^1.1.1":
  version: 1.1.2
  resolution: "strnum@npm:1.1.2"
  checksum: 10c0/a0fce2498fa3c64ce64a40dada41beb91cabe3caefa910e467dc0518ef2ebd7e4d10f8c2202a6104f1410254cae245066c0e94e2521fb4061a5cb41831952392
  languageName: node
  linkType: hard

"style-to-js@npm:^1.0.0":
  version: 1.1.16
  resolution: "style-to-js@npm:1.1.16"
  dependencies:
    style-to-object: "npm:1.0.8"
  checksum: 10c0/578a4dff804539ec7e64d3cc8d327540befb9ad30e3cd0b6b0392f93f793f3a028f90084a9aaff088bffb87818fa2c6c153f0df576f61f9ab0b0938b582bcac7
  languageName: node
  linkType: hard

"style-to-object@npm:1.0.8":
  version: 1.0.8
  resolution: "style-to-object@npm:1.0.8"
  dependencies:
    inline-style-parser: "npm:0.2.4"
  checksum: 10c0/daa6646b1ff18258c0ca33ed281fbe73485c8391192db1b56ce89d40c93ea64507a41e8701d0dadfe771bc2f540c46c9b295135f71584c8e5cb23d6a19be9430
  languageName: node
  linkType: hard

"stylis@npm:^4.3.0":
  version: 4.3.6
  resolution: "stylis@npm:4.3.6"
  checksum: 10c0/e736d484983a34f7c65d362c67dc79b7bce388054b261c2b7b23d02eaaf280617033f65d44b1ea341854f4331a5074b885668ac8741f98c13a6cfd6443ae85d0
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/afb4c88521b8b136b5f5f95160c98dee7243dc79d5432db7efc27efb219385bbc7d9427398e43dd6cc730a0f87d5085ce1652af7efbe391327bc0a7d0f7fc124
  languageName: node
  linkType: hard

"tailwind-merge@npm:^3.3.0":
  version: 3.3.0
  resolution: "tailwind-merge@npm:3.3.0"
  checksum: 10c0/a50cd141100486f98541dfab3705712af5860556689b7496dc6b0284374f02d12d5471f0f40035f6bb8b1c749c422060a1f3e5f8900057d8a7786b111c8472e6
  languageName: node
  linkType: hard

"tailwindcss@npm:4.1.8, tailwindcss@npm:^4.0.15":
  version: 4.1.8
  resolution: "tailwindcss@npm:4.1.8"
  checksum: 10c0/a566d049ee313f9c1638fd2a5fe95ae7e40bb35b58e92ed8d7c51880e71dff2a423dd430ff97444e2496c9c77b3f44ad4df48110b11484133019c1a8520d7bce
  languageName: node
  linkType: hard

"tapable@npm:^2.2.0":
  version: 2.2.2
  resolution: "tapable@npm:2.2.2"
  checksum: 10c0/8ad130aa705cab6486ad89e42233569a1fb1ff21af115f59cebe9f2b45e9e7995efceaa9cc5062510cdb4ec673b527924b2ab812e3579c55ad659ae92117011e
  languageName: node
  linkType: hard

"tar-fs@npm:^2.0.0":
  version: 2.1.3
  resolution: "tar-fs@npm:2.1.3"
  dependencies:
    chownr: "npm:^1.1.1"
    mkdirp-classic: "npm:^0.5.2"
    pump: "npm:^3.0.0"
    tar-stream: "npm:^2.1.4"
  checksum: 10c0/472ee0c3c862605165163113ab6924f411c07506a1fb24c51a1a80085f0d4d381d86d2fd6b189236c8d932d1cd97b69cce35016767ceb658a35f7584fe77f305
  languageName: node
  linkType: hard

"tar-fs@npm:^3.0.4":
  version: 3.0.9
  resolution: "tar-fs@npm:3.0.9"
  dependencies:
    bare-fs: "npm:^4.0.1"
    bare-path: "npm:^3.0.0"
    pump: "npm:^3.0.0"
    tar-stream: "npm:^3.1.5"
  dependenciesMeta:
    bare-fs:
      optional: true
    bare-path:
      optional: true
  checksum: 10c0/e7c6c8b7d1bf342bab0e94e0a2d96dc73c18d0cc6b59ee7b57f919adb08f5cee7f417cb4baee8322789292dc51ae67028cfd5d73f3559c919723ec7d71aa8959
  languageName: node
  linkType: hard

"tar-stream@npm:^2.1.4":
  version: 2.2.0
  resolution: "tar-stream@npm:2.2.0"
  dependencies:
    bl: "npm:^4.0.3"
    end-of-stream: "npm:^1.4.1"
    fs-constants: "npm:^1.0.0"
    inherits: "npm:^2.0.3"
    readable-stream: "npm:^3.1.1"
  checksum: 10c0/2f4c910b3ee7196502e1ff015a7ba321ec6ea837667220d7bcb8d0852d51cb04b87f7ae471008a6fb8f5b1a1b5078f62f3a82d30c706f20ada1238ac797e7692
  languageName: node
  linkType: hard

"tar-stream@npm:^3.1.5":
  version: 3.1.7
  resolution: "tar-stream@npm:3.1.7"
  dependencies:
    b4a: "npm:^1.6.4"
    fast-fifo: "npm:^1.2.0"
    streamx: "npm:^2.15.0"
  checksum: 10c0/a09199d21f8714bd729993ac49b6c8efcb808b544b89f23378ad6ffff6d1cb540878614ba9d4cfec11a64ef39e1a6f009a5398371491eb1fda606ffc7f70f718
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10c0/d4679609bb2a9b48eeaf84632b6d844128d2412b95b6de07d53d8ee8baf4ca0857c9331dfa510390a0727b550fd543d4d1a10995ad86cdf078423fbb8d99831d
  languageName: node
  linkType: hard

"text-decoder@npm:^1.1.0":
  version: 1.2.3
  resolution: "text-decoder@npm:1.2.3"
  dependencies:
    b4a: "npm:^1.6.4"
  checksum: 10c0/569d776b9250158681c83656ef2c3e0a5d5c660c27ca69f87eedef921749a4fbf02095e5f9a0f862a25cf35258379b06e31dee9c125c9f72e273b7ca1a6d1977
  languageName: node
  linkType: hard

"throttle-debounce@npm:^3.0.1":
  version: 3.0.1
  resolution: "throttle-debounce@npm:3.0.1"
  checksum: 10c0/c8e558479463b7ed8bac30d6b10cc87abd1c9fc64edfce2db4109be1a04acaef5d2d0557f49c1a3845ea07d9f79e6e0389b1b60db0a77c44e5b7a1216596f285
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12, tinyglobby@npm:^0.2.13":
  version: 0.2.14
  resolution: "tinyglobby@npm:0.2.14"
  dependencies:
    fdir: "npm:^6.4.4"
    picomatch: "npm:^4.0.2"
  checksum: 10c0/f789ed6c924287a9b7d3612056ed0cda67306cd2c80c249fd280cf1504742b12583a2089b61f4abbd24605f390809017240e250241f09938054c9b363e51c0a6
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10c0/487988b0a19c654ff3e1961b87f471702e708fa8a8dd02a298ef16da7206692e8552a0250e8b3e8759270f62e9d8314616f6da274734d3b558b1fc7b7724e892
  languageName: node
  linkType: hard

"toggle-selection@npm:^1.0.6":
  version: 1.0.6
  resolution: "toggle-selection@npm:1.0.6"
  checksum: 10c0/f2cf1f2c70f374fd87b0cdc8007453ba9e981c4305a8bf4eac10a30e62ecdfd28bca7d18f8f15b15a506bf8a7bfb20dbe3539f0fcf2a2c8396c1a78d53e1f179
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 10c0/93937279934bd66cc3270016dd8d0afec14fb7c94a05c72dc57321f8bd1fa97e5bea6d1f7c89e728d077ca31ea125b78320a616a6c6cd0e6b9cb94cb864381c1
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 10c0/047cb209a6b60c742f05c9d3ace8fa510bff609995c129a37ace03476a9b12db4dbf975e74600830ef0796e18882b2381fb5fb1f6b4f96b832c374de3ab91a11
  languageName: node
  linkType: hard

"trim-lines@npm:^3.0.0":
  version: 3.0.1
  resolution: "trim-lines@npm:3.0.1"
  checksum: 10c0/3a1611fa9e52aa56a94c69951a9ea15b8aaad760eaa26c56a65330dc8adf99cb282fc07cc9d94968b7d4d88003beba220a7278bbe2063328eb23fb56f9509e94
  languageName: node
  linkType: hard

"trough@npm:^2.0.0":
  version: 2.2.0
  resolution: "trough@npm:2.2.0"
  checksum: 10c0/58b671fc970e7867a48514168894396dd94e6d9d6456aca427cc299c004fe67f35ed7172a36449086b2edde10e78a71a284ec0076809add6834fb8f857ccb9b0
  languageName: node
  linkType: hard

"ts-api-utils@npm:^2.1.0":
  version: 2.1.0
  resolution: "ts-api-utils@npm:2.1.0"
  peerDependencies:
    typescript: ">=4.8.4"
  checksum: 10c0/9806a38adea2db0f6aa217ccc6bc9c391ddba338a9fe3080676d0d50ed806d305bb90e8cef0276e793d28c8a929f400abb184ddd7ff83a416959c0f4d2ce754f
  languageName: node
  linkType: hard

"ts-easing@npm:^0.2.0":
  version: 0.2.0
  resolution: "ts-easing@npm:0.2.0"
  checksum: 10c0/84ec20192310c697ff890ca2e0625e131a32596a7c5956326c9632faca9037abf2dd3de4d81ac358ae9f26a6a2cfe2300f13756b26995f753d882e3d0463e327
  languageName: node
  linkType: hard

"tslib@npm:^2.0.0, tslib@npm:^2.1.0, tslib@npm:^2.4.0, tslib@npm:^2.8.0":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 10c0/9c4759110a19c53f992d9aae23aac5ced636e99887b51b9e61def52611732872ff7668757d4e4c61f19691e36f4da981cd9485e869b4a7408d689f6bf1f14e62
  languageName: node
  linkType: hard

"tunnel-agent@npm:^0.6.0":
  version: 0.6.0
  resolution: "tunnel-agent@npm:0.6.0"
  dependencies:
    safe-buffer: "npm:^5.0.1"
  checksum: 10c0/4c7a1b813e7beae66fdbf567a65ec6d46313643753d0beefb3c7973d66fcec3a1e7f39759f0a0b4465883499c6dc8b0750ab8b287399af2e583823e40410a17a
  languageName: node
  linkType: hard

"tw-animate-css@npm:^1.3.0":
  version: 1.3.4
  resolution: "tw-animate-css@npm:1.3.4"
  checksum: 10c0/fc17e27684351ee151dd0bcef848ef72d1ec4bce6769ece079f30ac8a481c52a6c9d523f7ecd55ef73adc4b7375fdc7a46bb2598335fb06d86d0bc65a6c3e37b
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: "npm:^1.2.1"
  checksum: 10c0/7b3fd0ed43891e2080bf0c5c504b418fbb3e5c7b9708d3d015037ba2e6323a28152ec163bcb65212741fa5d2022e3075ac3c76440dbd344c9035f818e8ecee58
  languageName: node
  linkType: hard

"type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: "npm:0.3.0"
    mime-types: "npm:~2.1.24"
  checksum: 10c0/a23daeb538591b7efbd61ecf06b6feb2501b683ffdc9a19c74ef5baba362b4347e42f1b4ed81f5882a8c96a3bfff7f93ce3ffaf0cbbc879b532b04c97a55db9d
  languageName: node
  linkType: hard

"typescript-eslint@npm:^8.24.1":
  version: 8.33.1
  resolution: "typescript-eslint@npm:8.33.1"
  dependencies:
    "@typescript-eslint/eslint-plugin": "npm:8.33.1"
    "@typescript-eslint/parser": "npm:8.33.1"
    "@typescript-eslint/utils": "npm:8.33.1"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10c0/8b332c565008f975e0905b99705214c4d58f55a4ff7186edda6a77e041a3e2f6fbbb5a78192ff3c77ccb385b624cf222bca0856c138dfd1fe8875aa3dab38f2c
  languageName: node
  linkType: hard

"typescript@npm:~5.7.2":
  version: 5.7.3
  resolution: "typescript@npm:5.7.3"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/b7580d716cf1824736cc6e628ab4cd8b51877408ba2be0869d2866da35ef8366dd6ae9eb9d0851470a39be17cbd61df1126f9e211d8799d764ea7431d5435afa
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A~5.7.2#optional!builtin<compat/typescript>":
  version: 5.7.3
  resolution: "typescript@patch:typescript@npm%3A5.7.3#optional!builtin<compat/typescript>::version=5.7.3&hash=5786d5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/6fd7e0ed3bf23a81246878c613423730c40e8bdbfec4c6e4d7bf1b847cbb39076e56ad5f50aa9d7ebd89877999abaee216002d3f2818885e41c907caaa192cc4
  languageName: node
  linkType: hard

"undici-types@npm:~5.26.4":
  version: 5.26.5
  resolution: "undici-types@npm:5.26.5"
  checksum: 10c0/bb673d7876c2d411b6eb6c560e0c571eef4a01c1c19925175d16e3a30c4c428181fb8d7ae802a261f283e4166a0ac435e2f505743aa9e45d893f9a3df017b501
  languageName: node
  linkType: hard

"undici-types@npm:~6.21.0":
  version: 6.21.0
  resolution: "undici-types@npm:6.21.0"
  checksum: 10c0/c01ed51829b10aa72fc3ce64b747f8e74ae9b60eafa19a7b46ef624403508a54c526ffab06a14a26b3120d055e1104d7abe7c9017e83ced038ea5cf52f8d5e04
  languageName: node
  linkType: hard

"unified@npm:^11.0.0":
  version: 11.0.5
  resolution: "unified@npm:11.0.5"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    bail: "npm:^2.0.0"
    devlop: "npm:^1.0.0"
    extend: "npm:^3.0.0"
    is-plain-obj: "npm:^4.0.0"
    trough: "npm:^2.0.0"
    vfile: "npm:^6.0.0"
  checksum: 10c0/53c8e685f56d11d9d458a43e0e74328a4d6386af51c8ac37a3dcabec74ce5026da21250590d4aff6733ccd7dc203116aae2b0769abc18cdf9639a54ae528dfc9
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10c0/38ae681cceb1408ea0587b6b01e29b00eee3c84baee1e41fd5c16b9ed443b80fba90c40e0ba69627e30855570a34ba8b06702d4a35035d4b5e198bf5a64c9ddc
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10c0/d324c5a44887bd7e105ce800fcf7533d43f29c48757ac410afd42975de82cc38ea2035c0483f4de82d186691bf3208ef35c644f73aa2b1b20b8e651be5afd293
  languageName: node
  linkType: hard

"unist-util-is@npm:^6.0.0":
  version: 6.0.0
  resolution: "unist-util-is@npm:6.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
  checksum: 10c0/9419352181eaa1da35eca9490634a6df70d2217815bb5938a04af3a662c12c5607a2f1014197ec9c426fbef18834f6371bfdb6f033040fa8aa3e965300d70e7e
  languageName: node
  linkType: hard

"unist-util-position@npm:^5.0.0":
  version: 5.0.0
  resolution: "unist-util-position@npm:5.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
  checksum: 10c0/dde3b31e314c98f12b4dc6402f9722b2bf35e96a4f2d463233dd90d7cde2d4928074a7a11eff0a5eb1f4e200f27fc1557e0a64a7e8e4da6558542f251b1b7400
  languageName: node
  linkType: hard

"unist-util-stringify-position@npm:^4.0.0":
  version: 4.0.0
  resolution: "unist-util-stringify-position@npm:4.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
  checksum: 10c0/dfe1dbe79ba31f589108cb35e523f14029b6675d741a79dea7e5f3d098785045d556d5650ec6a8338af11e9e78d2a30df12b1ee86529cded1098da3f17ee999e
  languageName: node
  linkType: hard

"unist-util-visit-parents@npm:^6.0.0":
  version: 6.0.1
  resolution: "unist-util-visit-parents@npm:6.0.1"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    unist-util-is: "npm:^6.0.0"
  checksum: 10c0/51b1a5b0aa23c97d3e03e7288f0cdf136974df2217d0999d3de573c05001ef04cccd246f51d2ebdfb9e8b0ed2704451ad90ba85ae3f3177cf9772cef67f56206
  languageName: node
  linkType: hard

"unist-util-visit@npm:^5.0.0":
  version: 5.0.0
  resolution: "unist-util-visit@npm:5.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    unist-util-is: "npm:^6.0.0"
    unist-util-visit-parents: "npm:^6.0.0"
  checksum: 10c0/51434a1d80252c1540cce6271a90fd1a106dbe624997c09ed8879279667fb0b2d3a685e02e92bf66598dcbe6cdffa7a5f5fb363af8fdf90dda6c855449ae39a5
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0, unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 10c0/193400255bd48968e5c5383730344fbb4fa114cdedfab26e329e50dd2d81b134244bb8a72c6ac1b10ab0281a58b363d06405632c9d49ca9dfd5e90cbd7d0f32c
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.3":
  version: 1.1.3
  resolution: "update-browserslist-db@npm:1.1.3"
  dependencies:
    escalade: "npm:^3.2.0"
    picocolors: "npm:^1.1.1"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 10c0/682e8ecbf9de474a626f6462aa85927936cdd256fe584c6df2508b0df9f7362c44c957e9970df55dfe44d3623807d26316ea2c7d26b80bb76a16c56c37233c32
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 10c0/4ef57b45aa820d7ac6496e9208559986c665e49447cb072744c13b66925a362d96dd5a46c4530a6b8e203e5db5fe849369444440cb22ecfc26c679359e5dfa3c
  languageName: node
  linkType: hard

"use-callback-ref@npm:^1.3.3":
  version: 1.3.3
  resolution: "use-callback-ref@npm:1.3.3"
  dependencies:
    tslib: "npm:^2.0.0"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/f887488c6e6075cdad4962979da1714b217bcb1ee009a9e57ce9a844bcfc4c3a99e93983dfc2e5af9e0913824d24e730090ff255e902c516dcb58d2d3837e01c
  languageName: node
  linkType: hard

"use-sidecar@npm:^1.1.3":
  version: 1.1.3
  resolution: "use-sidecar@npm:1.1.3"
  dependencies:
    detect-node-es: "npm:^1.1.0"
    tslib: "npm:^2.0.0"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/161599bf921cfaa41c85d2b01c871975ee99260f3e874c2d41c05890d41170297bdcf314bc5185e7a700de2034ac5b888e3efc8e9f35724f4918f53538d717c9
  languageName: node
  linkType: hard

"use-sync-external-store@npm:^1.5.0":
  version: 1.5.0
  resolution: "use-sync-external-store@npm:1.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10c0/1b8663515c0be34fa653feb724fdcce3984037c78dd4a18f68b2c8be55cc1a1084c578d5b75f158d41b5ddffc2bf5600766d1af3c19c8e329bb20af2ec6f52f4
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10c0/41a5bdd214df2f6c3ecf8622745e4a366c4adced864bc3c833739791aeeeb1838119af7daed4ba36428114b5c67dcda034a79c882e97e43c03e66a4dd7389942
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: 10c0/02ba649de1b7ca8854bfe20a82f1dfbdda3fb57a22ab4a8972a63a34553cf7aa51bc9081cf7e001b035b88186d23689d69e71b510e610a09a4c66f68aa95b672
  languageName: node
  linkType: hard

"uuid@npm:^10.0.0":
  version: 10.0.0
  resolution: "uuid@npm:10.0.0"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/eab18c27fe4ab9fb9709a5d5f40119b45f2ec8314f8d4cf12ce27e4c6f4ffa4a6321dc7db6c515068fa373c075b49691ba969f0010bf37f44c37ca40cd6bf7fe
  languageName: node
  linkType: hard

"uuid@npm:^9.0.0":
  version: 9.0.1
  resolution: "uuid@npm:9.0.1"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/1607dd32ac7fc22f2d8f77051e6a64845c9bce5cd3dd8aa0070c074ec73e666a1f63c7b4e0f4bf2bc8b9d59dc85a15e17807446d9d2b17c8485fbc2147b27f9b
  languageName: node
  linkType: hard

"vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: 10c0/f15d588d79f3675135ba783c91a4083dcd290a2a5be9fcb6514220a1634e23df116847b1cc51f66bfb0644cf9353b2abb7815ae499bab06e46dd33c1a6bf1f4f
  languageName: node
  linkType: hard

"vfile-message@npm:^4.0.0":
  version: 4.0.2
  resolution: "vfile-message@npm:4.0.2"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    unist-util-stringify-position: "npm:^4.0.0"
  checksum: 10c0/07671d239a075f888b78f318bc1d54de02799db4e9dce322474e67c35d75ac4a5ac0aaf37b18801d91c9f8152974ea39678aa72d7198758b07f3ba04fb7d7514
  languageName: node
  linkType: hard

"vfile@npm:^6.0.0":
  version: 6.0.3
  resolution: "vfile@npm:6.0.3"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    vfile-message: "npm:^4.0.0"
  checksum: 10c0/e5d9eb4810623f23758cfc2205323e33552fb5972e5c2e6587babe08fe4d24859866277404fb9e2a20afb71013860d96ec806cb257536ae463c87d70022ab9ef
  languageName: node
  linkType: hard

"vite@npm:^6.2.0":
  version: 6.3.5
  resolution: "vite@npm:6.3.5"
  dependencies:
    esbuild: "npm:^0.25.0"
    fdir: "npm:^6.4.4"
    fsevents: "npm:~2.3.3"
    picomatch: "npm:^4.0.2"
    postcss: "npm:^8.5.3"
    rollup: "npm:^4.34.9"
    tinyglobby: "npm:^0.2.13"
  peerDependencies:
    "@types/node": ^18.0.0 || ^20.0.0 || >=22.0.0
    jiti: ">=1.21.0"
    less: "*"
    lightningcss: ^1.21.0
    sass: "*"
    sass-embedded: "*"
    stylus: "*"
    sugarss: "*"
    terser: ^5.16.0
    tsx: ^4.8.1
    yaml: ^2.4.2
  dependenciesMeta:
    fsevents:
      optional: true
  peerDependenciesMeta:
    "@types/node":
      optional: true
    jiti:
      optional: true
    less:
      optional: true
    lightningcss:
      optional: true
    sass:
      optional: true
    sass-embedded:
      optional: true
    stylus:
      optional: true
    sugarss:
      optional: true
    terser:
      optional: true
    tsx:
      optional: true
    yaml:
      optional: true
  bin:
    vite: bin/vite.js
  checksum: 10c0/df70201659085133abffc6b88dcdb8a57ef35f742a01311fc56a4cfcda6a404202860729cc65a2c401a724f6e25f9ab40ce4339ed4946f550541531ced6fe41c
  languageName: node
  linkType: hard

"web-streams-polyfill@npm:4.0.0-beta.3":
  version: 4.0.0-beta.3
  resolution: "web-streams-polyfill@npm:4.0.0-beta.3"
  checksum: 10c0/a9596779db2766990117ed3a158e0b0e9f69b887a6d6ba0779940259e95f99dc3922e534acc3e5a117b5f5905300f527d6fbf8a9f0957faf1d8e585ce3452e8e
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: 10c0/5612d5f3e54760a797052eb4927f0ddc01383550f542ccd33d5238cfd65aeed392a45ad38364970d0a0f4fea32e1f4d231b3d8dac4a3bdd385e5cf802ae097db
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: "npm:~0.0.3"
    webidl-conversions: "npm:^3.0.0"
  checksum: 10c0/1588bed84d10b72d5eec1d0faa0722ba1962f1821e7539c535558fb5398d223b0c50d8acab950b8c488b4ba69043fd833cc2697056b167d8ad46fac3995a55d5
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10c0/66522872a768b60c2a65a57e8ad184e5372f5b6a9ca6d5f033d4b0dc98aff63995655a7503b9c0a2598936f532120e81dd8cc155e2e92ed662a2b9377cc4374f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10c0/e556e4cd8b7dbf5df52408c9a9dd5ac6518c8c5267c8953f5b0564073c66ed5bf9503b14d876d0e9c7844d4db9725fb0dcf45d6e911e17e26ab363dc3965ae7b
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: 10c0/e0e4a1ca27599c92a6ca4c32260e8a92e8a44f4ef6ef93f803f8ed823f486e0889fc0b93be4db59c8d51b3064951d25e43d434e95dc8c960cc3a63d65d00ba20
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/d15fc12c11e4cbc4044a552129ebc75ee3f57aa9c1958373a4db0292d72282f54373b536103987a4a7594db1ef6a4f10acf92978f79b98c49306a4b58c77d4da
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/138ff58a41d2f877eae87e3282c0630fc2789012fc1af4d6bd626eeb9a2f9a65ca92005e6e69a75c7b85a68479fe7443c7dbe1eb8fbaa681a4491364b7c55c60
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 10c0/56fece1a4018c6a6c8e28fbc88c87e0fbf4ea8fd64fc6c63b18f4acc4bd13e0ad2515189786dd2c30d3eec9663d70f4ecf699330002f8ccb547e4a18231fc9f0
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 10c0/c66a5c46bc89af1625476f7f0f2ec3653c1a1791d2f9407cfb4c2ba812a1e1c9941416d71ba9719876530e3340a99925f697142989371b72d93b9ee628afd8c1
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10c0/2286b5e8dbfe22204ab66e2ef5cc9bbb1e55dfc873bbe0d568aa943eb255d131890dfd5bf243637273d31119b870f49c18fcde2c6ffbb7a7a092b870dc90625a
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10c0/a499c81ce6d4a1d260d4ea0f6d49ab4da09681e32c3f0472dee16667ed69d01dae63a3b81745a24bd78476ec4fcf856114cb4896ace738e01da34b2c42235416
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: 10c0/dceb44c28578b31641e13695d200d34ec4ab3966a5729814d5445b194933c096b7ced71494ce53a0e8820685d1d010df8b2422e5bf2cdea7e469d97ffbea306f
  languageName: node
  linkType: hard

"zod-to-json-schema@npm:^3.22.3, zod-to-json-schema@npm:^3.22.4":
  version: 3.24.5
  resolution: "zod-to-json-schema@npm:3.24.5"
  peerDependencies:
    zod: ^3.24.1
  checksum: 10c0/0745b94ba53e652d39f262641cdeb2f75d24339fb6076a38ce55bcf53d82dfaea63adf524ebc5f658681005401687f8e9551c4feca7c4c882e123e66091dfb90
  languageName: node
  linkType: hard

"zod@npm:^3.22.4, zod@npm:^3.23.8, zod@npm:^3.24.1":
  version: 3.25.56
  resolution: "zod@npm:3.25.56"
  checksum: 10c0/3800f01d4b1df932b91354eb1e648f69cc7e5561549e6d2bf83827d930a5f33bbf92926099445f6fc1ebb64ca9c6513ef9ae5e5409cfef6325f354bcf6fc9a24
  languageName: node
  linkType: hard

"zwitch@npm:^2.0.0":
  version: 2.0.4
  resolution: "zwitch@npm:2.0.4"
  checksum: 10c0/3c7830cdd3378667e058ffdb4cf2bb78ac5711214e2725900873accb23f3dfe5f9e7e5a06dcdc5f29605da976fc45c26d9a13ca334d6eea2245a15e77b8fc06e
  languageName: node
  linkType: hard
